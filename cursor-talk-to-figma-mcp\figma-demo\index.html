<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业无人机地面站控制系统</title>
    <!-- 引入图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <!-- 引入Leaflet地图库 -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" 
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" 
          crossorigin=""/>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
            background: #0a0f1a;
            color: #ffffff;
            overflow: hidden;
            height: 100vh;
        }

        .main-container {
            display: flex;
            height: 100vh;
            background: 
                radial-gradient(circle at 20% 80%, rgba(126, 211, 33, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(65, 105, 225, 0.1) 0%, transparent 50%),
                linear-gradient(135deg, #0a0f1a 0%, #1a2332 100%);
        }

        /* ================================ 左侧面板 ================================ */
        .left-panel {
            width: 280px;
            background: rgba(15, 25, 40, 0.95);
            border-right: 1px solid rgba(126, 211, 33, 0.3);
            padding: 20px;
            overflow-y: auto;
            box-shadow: inset -1px 0 10px rgba(126, 211, 33, 0.1);
        }

        .panel-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(126, 211, 33, 0.3);
        }

        .panel-header i {
            color: #7ed321;
            font-size: 18px;
        }

        .panel-title {
            color: #7ed321;
            font-size: 16px;
            font-weight: bold;
            text-shadow: 0 0 10px rgba(126, 211, 33, 0.5);
        }

        .mission-section {
            margin-bottom: 25px;
        }

        .mission-item {
            background: linear-gradient(135deg, rgba(126, 211, 33, 0.15), rgba(126, 211, 33, 0.05));
            border: 1px solid rgba(126, 211, 33, 0.5);
            border-left: 4px solid #7ed321;
            padding: 12px;
            margin-bottom: 10px;
            border-radius: 6px;
            font-size: 13px;
            color: #ffffff;
            position: relative;
            transition: all 0.3s ease;
        }

        .mission-item:hover {
            background: linear-gradient(135deg, rgba(126, 211, 33, 0.25), rgba(126, 211, 33, 0.1));
            transform: translateX(3px);
        }

        .mission-item::before {
            content: '';
            position: absolute;
            top: 50%;
            right: 12px;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: #7ed321;
            border-radius: 50%;
            animation: pulse-dot 2s infinite;
        }

        @keyframes pulse-dot {
            0%, 100% { opacity: 1; transform: translateY(-50%) scale(1); }
            50% { opacity: 0.5; transform: translateY(-50%) scale(1.2); }
        }

        .mission-status {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
            font-size: 11px;
            color: #7ed321;
        }

        .mission-progress {
            flex: 1;
            height: 4px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            overflow: hidden;
        }

        .mission-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #7ed321, #9fff40);
            transition: width 0.5s ease;
        }

        /* 无人机列表 */
        .drone-list {
            margin-top: 25px;
        }

        .drone-item {
            background: linear-gradient(135deg, rgba(126, 211, 33, 0.1), rgba(15, 25, 40, 0.8));
            border: 1px solid rgba(126, 211, 33, 0.4);
            padding: 15px;
            margin-bottom: 12px;
            border-radius: 8px;
            position: relative;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .drone-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #7ed321, transparent);
            animation: scan 3s linear infinite;
        }

        @keyframes scan {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .drone-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .drone-id-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .drone-id {
            color: #000;
            background: linear-gradient(135deg, #7ed321, #9fff40);
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 12px;
            box-shadow: 0 2px 4px rgba(126, 211, 33, 0.3);
        }

        .drone-signal {
            display: flex;
            gap: 2px;
        }

        .signal-bar {
            width: 3px;
            height: 12px;
            background: rgba(126, 211, 33, 0.3);
            border-radius: 1px;
        }

        .signal-bar.active {
            background: #7ed321;
            animation: signal-pulse 1s ease-in-out infinite alternate;
        }

        @keyframes signal-pulse {
            from { opacity: 0.7; }
            to { opacity: 1; }
        }

        .drone-status-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin-top: 8px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 10px;
            color: rgba(255, 255, 255, 0.8);
        }

        .status-indicator i {
            color: #7ed321;
            font-size: 12px;
        }

        .status-indicator.warning i {
            color: #ffd700;
        }

        .status-indicator.error i {
            color: #ff6b47;
        }

        .battery-container {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
        }

        .battery-icon {
            color: #7ed321;
            font-size: 14px;
        }

        .battery-bar {
            flex: 1;
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            overflow: hidden;
            border: 1px solid rgba(126, 211, 33, 0.3);
        }

        .battery-fill {
            height: 100%;
            background: linear-gradient(90deg, #7ed321, #9fff40);
            transition: width 0.5s ease;
            position: relative;
        }

        .battery-fill::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 20%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
            animation: battery-shine 2s ease-in-out infinite;
        }

        @keyframes battery-shine {
            0%, 100% { opacity: 0; }
            50% { opacity: 1; }
        }

        .battery-percentage {
            font-size: 11px;
            color: #7ed321;
            font-weight: bold;
            min-width: 35px;
        }

        /* 数据统计图表 */
        .stats-section {
            margin-top: 25px;
        }

        .stats-chart {
            background: linear-gradient(135deg, rgba(126, 211, 33, 0.1), rgba(15, 25, 40, 0.8));
            height: 140px;
            border-radius: 8px;
            border: 1px solid rgba(126, 211, 33, 0.4);
            position: relative;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .chart-grid {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(rgba(126, 211, 33, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(126, 211, 33, 0.1) 1px, transparent 1px);
            background-size: 20px 20px;
        }

        .chart-line {
            position: absolute;
            bottom: 20px;
            left: 10px;
            right: 10px;
            height: 80px;
        }

        .chart-path {
            fill: none;
            stroke: #7ed321;
            stroke-width: 2;
            filter: drop-shadow(0 0 5px rgba(126, 211, 33, 0.7));
        }

        .chart-area {
            fill: url(#chartGradient);
            opacity: 0.3;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            font-size: 11px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            background: rgba(126, 211, 33, 0.05);
            border-radius: 4px;
            border-left: 3px solid #7ed321;
        }

        .stat-value {
            color: #7ed321;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        /* ================================ 中央地图区域 ================================ */
        .map-container {
            flex: 1;
            position: relative;
            background: 
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(126,211,33,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)" /></svg>'),
                radial-gradient(circle at 30% 70%, rgba(126, 211, 33, 0.05) 0%, transparent 50%),
                linear-gradient(135deg, #1a2332 0%, #0f1a29 100%);
        }

        /* 真实地图显示区域 */
        #realMap {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
        }

        /* 自定义地图样式 */
        .leaflet-container {
            background: #1a2332 !important;
        }

        /* 隐藏Leaflet默认的放大缩小按钮，使用我们自己的 */
        .leaflet-control-zoom {
            display: none !important;
        }

        /* 自定义地图标记样式 */
        .custom-drone-marker {
            width: 60px;
            height: 60px;
            background: radial-gradient(circle, rgba(126, 211, 33, 0.8) 30%, transparent 70%);
            border: 3px solid #7ed321;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #7ed321;
            animation: drone-pulse 2s infinite;
            box-shadow: 0 0 20px rgba(126, 211, 33, 0.5);
        }

        @keyframes drone-pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
        }

        /* 任务区域自定义样式 */
        .custom-mission-marker {
            background: linear-gradient(135deg, rgba(126, 211, 33, 0.2), rgba(126, 211, 33, 0.1));
            border: 2px dashed #7ed321;
            padding: 15px;
            border-radius: 8px;
            color: #7ed321;
            font-size: 12px;
            text-align: center;
            min-width: 120px;
            backdrop-filter: blur(5px);
        }

        .map-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 60% 40%, rgba(65, 105, 225, 0.08) 0%, transparent 60%),
                linear-gradient(45deg, 
                    rgba(26, 35, 50, 0.6) 0%, 
                    rgba(20, 30, 45, 0.3) 50%, 
                    rgba(26, 35, 50, 0.6) 100%);
        }

        .map-header {
            position: absolute;
            top: 15px;
            left: 20px;
            right: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 10;
            background: rgba(15, 25, 40, 0.9);
            padding: 12px 20px;
            border-radius: 8px;
            border: 1px solid rgba(126, 211, 33, 0.3);
            backdrop-filter: blur(10px);
        }

        .header-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
        }

        .header-item i {
            color: #7ed321;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #7ed321;
            border-radius: 50%;
            animation: status-blink 2s infinite;
        }

        @keyframes status-blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        /* 无人机标记增强 */
        .drone-marker {
            position: absolute;
            width: 60px;
            height: 60px;
            transform: translate(-50%, -50%);
            z-index: 5;
        }

        .drone-radar {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100px;
            height: 100px;
            border: 2px solid rgba(126, 211, 33, 0.3);
            border-radius: 50%;
            animation: radar-pulse 3s infinite;
        }

        @keyframes radar-pulse {
            0% { 
                transform: translate(-50%, -50%) scale(0.5);
                opacity: 1;
            }
            100% { 
                transform: translate(-50%, -50%) scale(2);
                opacity: 0;
            }
        }

        .drone-icon-container {
            position: relative;
            width: 60px;
            height: 60px;
            background: radial-gradient(circle, rgba(126, 211, 33, 0.3) 30%, transparent 70%);
            border: 3px solid #7ed321;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            animation: drone-float 4s ease-in-out infinite;
            box-shadow: 
                0 0 20px rgba(126, 211, 33, 0.5),
                inset 0 0 10px rgba(126, 211, 33, 0.2);
        }

        @keyframes drone-float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        .drone-direction {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            color: #7ed321;
            font-size: 16px;
            filter: drop-shadow(0 0 5px rgba(126, 211, 33, 0.7));
        }

        .drone-trail {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 200px;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(126, 211, 33, 0.5), transparent);
            transform: translate(-50%, -50%) rotate(-45deg);
            animation: trail-flow 2s linear infinite;
        }

        @keyframes trail-flow {
            0% { opacity: 0; transform: translate(-50%, -50%) rotate(-45deg) translateX(-100px); }
            50% { opacity: 1; }
            100% { opacity: 0; transform: translate(-50%, -50%) rotate(-45deg) translateX(100px); }
        }

        /* 任务区域增强 */
        .mission-marker {
            position: absolute;
            transform: translate(-50%, -50%);
            z-index: 4;
        }

        .mission-box {
            background: linear-gradient(135deg, rgba(126, 211, 33, 0.15), rgba(126, 211, 33, 0.05));
            border: 2px dashed #7ed321;
            padding: 20px;
            border-radius: 12px;
            min-width: 180px;
            text-align: center;
            position: relative;
            backdrop-filter: blur(5px);
        }

        .mission-box::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: linear-gradient(45deg, transparent, rgba(126, 211, 33, 0.1), transparent);
            border-radius: 12px;
            z-index: -1;
            animation: mission-glow 3s ease-in-out infinite;
        }

        @keyframes mission-glow {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .mission-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: #7ed321;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .mission-info {
            background: rgba(0, 0, 0, 0.8);
            color: #7ed321;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 11px;
            margin-top: 10px;
            border: 1px solid rgba(126, 211, 33, 0.3);
        }

        

        .map-control-btn {
            width: 44px;
            height: 44px;
            background: rgba(15, 25, 40, 0.9);
            border: 1px solid rgba(126, 211, 33, 0.4);
            border-radius: 8px;
            color: #7ed321;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 16px;
            backdrop-filter: blur(10px);
        }

        .map-control-btn:hover {
            background: rgba(126, 211, 33, 0.2);
            border-color: #7ed321;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(126, 211, 33, 0.3);
        }

                 /* 无人机操控按钮组 */
         .drone-controls {
             position: absolute;
             bottom: 40px;
             right: 80px;
             display: flex;
             gap: 10px;
             z-index: 10;
             padding: 15px;
             background: rgba(15, 25, 40, 0.95);
             border: 1px solid rgba(126, 211, 33, 0.4);
             border-radius: 12px;
             backdrop-filter: blur(15px);
             box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
         }

        .drone-control-btn {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, rgba(126, 211, 33, 0.1), rgba(15, 25, 40, 0.8));
            border: 2px solid rgba(126, 211, 33, 0.5);
            border-radius: 12px;
            color: #7ed321;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 11px;
            font-weight: bold;
            position: relative;
            overflow: hidden;
        }

        .drone-control-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(126, 211, 33, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .drone-control-btn:hover::before {
            left: 100%;
        }

        .drone-control-btn:hover {
            background: linear-gradient(135deg, rgba(126, 211, 33, 0.25), rgba(15, 25, 40, 0.9));
            border-color: #7ed321;
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(126, 211, 33, 0.4);
        }

        .drone-control-btn.active {
            background: linear-gradient(135deg, #7ed321, #9fff40);
            color: #000;
            border-color: #9fff40;
            box-shadow: 0 0 20px rgba(126, 211, 33, 0.7);
        }

        .drone-control-btn.emergency {
            border-color: #ff6b47;
            color: #ff6b47;
        }

        .drone-control-btn.emergency:hover {
            background: linear-gradient(135deg, rgba(255, 107, 71, 0.25), rgba(15, 25, 40, 0.9));
            border-color: #ff6b47;
            box-shadow: 0 6px 20px rgba(255, 107, 71, 0.4);
        }

        .drone-control-btn.emergency.active {
            background: linear-gradient(135deg, #ff6b47, #ff8a73);
            color: #fff;
            border-color: #ff8a73;
            box-shadow: 0 0 20px rgba(255, 107, 71, 0.7);
        }

        .drone-control-btn i {
            font-size: 28px;
            margin-bottom: 6px;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        .drone-control-btn .btn-label {
            font-size: 10px;
            text-align: center;
            line-height: 1.2;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        /* 操控按钮状态指示 */
        .control-status-indicator {
            position: absolute;
            top: 5px;
            right: 5px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #7ed321;
            animation: status-pulse 2s infinite;
        }

        .control-status-indicator.warning {
            background: #ffd700;
        }

        .control-status-indicator.error {
            background: #ff6b47;
        }

        @keyframes status-pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
        }

                 .terrain-map {
             position: absolute;
             bottom: 80px;
             left: 20px;
             width: 240px;
             height: 140px;
             background: rgba(15, 25, 40, 0.95);
             border: 1px solid rgba(126, 211, 33, 0.4);
             border-radius: 8px;
             z-index: 10;
             backdrop-filter: blur(10px);
             overflow: hidden;
         }

         /* 地图控制按钮调整到地形图下方 */
         .map-controls {
             position: absolute;
             bottom: 20px;
             left: 20px;
             width: 240px;
             display: flex;
             justify-content: center;
             gap: 8px;
             z-index: 10;
         }

        .terrain-header {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px;
            background: rgba(126, 211, 33, 0.1);
            border-bottom: 1px solid rgba(126, 211, 33, 0.3);
            color: #7ed321;
            font-size: 12px;
            font-weight: bold;
        }

        /* ================================ 右侧面板 ================================ */
        .right-panel {
            width: 380px;
            background: rgba(15, 25, 40, 0.95);
            border-left: 1px solid rgba(126, 211, 33, 0.3);
            display: flex;
            flex-direction: column;
            box-shadow: inset 1px 0 10px rgba(126, 211, 33, 0.1);
        }

        /* 视频监控区域增强 */
        .video-section {
            height: 320px;
            background: #000;
            border-bottom: 1px solid rgba(126, 211, 33, 0.3);
            position: relative;
        }

        .video-header {
            position: absolute;
            top: 12px;
            left: 12px;
            right: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 10;
        }

        .video-title {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #7ed321;
            font-size: 12px;
            background: rgba(0, 0, 0, 0.8);
            padding: 6px 10px;
            border-radius: 4px;
            border: 1px solid rgba(126, 211, 33, 0.3);
        }

        .video-controls {
            display: flex;
            gap: 6px;
        }

        .video-control-btn {
            width: 28px;
            height: 28px;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid rgba(126, 211, 33, 0.3);
            border-radius: 4px;
            color: #7ed321;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .video-control-btn:hover {
            background: rgba(126, 211, 33, 0.2);
        }

        .video-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            height: 100%;
            gap: 2px;
            padding: 2px;
        }

        .video-feed {
            background: linear-gradient(45deg, #1a2332, #2a3442);
            position: relative;
            overflow: hidden;
            border-radius: 4px;
            border: 1px solid rgba(126, 211, 33, 0.2);
        }

        .video-feed.main {
            grid-column: 1 / -1;
            grid-row: 1;
            height: 160px;
        }

        .video-label {
            position: absolute;
            top: 6px;
            left: 6px;
            background: linear-gradient(135deg, #7ed321, #9fff40);
            color: #000;
            font-size: 10px;
            padding: 3px 6px;
            border-radius: 3px;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .video-status {
            position: absolute;
            top: 6px;
            right: 6px;
            display: flex;
            gap: 4px;
        }

        .status-light {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #7ed321;
            animation: status-blink 1.5s infinite;
        }

        .status-light.recording {
            background: #ff3333;
        }

        /* 控制面板增强 */
        .control-section {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(126, 211, 33, 0.3);
        }

        .section-header i {
            color: #7ed321;
            font-size: 16px;
        }

        .section-title {
            color: #7ed321;
            font-size: 14px;
            font-weight: bold;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }

        .info-card {
            background: linear-gradient(135deg, rgba(126, 211, 33, 0.1), rgba(15, 25, 40, 0.8));
            border: 1px solid rgba(126, 211, 33, 0.3);
            border-radius: 6px;
            padding: 12px;
            position: relative;
        }

        .info-card-header {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 6px;
        }

        .info-card-header i {
            color: #7ed321;
            font-size: 14px;
        }

        .info-label {
            font-size: 10px;
            color: rgba(255, 255, 255, 0.7);
            text-transform: uppercase;
        }

        .info-value {
            color: #7ed321;
            font-size: 14px;
            font-weight: bold;
            margin-top: 4px;
        }

        /* 姿态指示器增强 */
        .attitude-indicator {
            width: 140px;
            height: 140px;
            border-radius: 50%;
            background: 
                radial-gradient(circle at 30% 30%, rgba(126, 211, 33, 0.2), transparent 50%),
                radial-gradient(circle, #1a2332 0%, #0a0f1a 100%);
            border: 3px solid #7ed321;
            margin: 20px auto;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 
                0 0 20px rgba(126, 211, 33, 0.3),
                inset 0 0 20px rgba(126, 211, 33, 0.1);
        }

        .attitude-inner {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: conic-gradient(from 0deg, #7ed321, #ffd700, #ff6b47, #7ed321);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #000;
            font-weight: bold;
            font-size: 12px;
            position: relative;
            animation: attitude-rotate 10s linear infinite;
        }

        @keyframes attitude-rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .attitude-markers {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 120px;
            height: 120px;
        }

        .attitude-marker {
            position: absolute;
            width: 2px;
            height: 10px;
            background: #7ed321;
            transform-origin: center 60px;
        }

        .attitude-marker:nth-child(1) { transform: translate(-50%, -50%) rotate(0deg); }
        .attitude-marker:nth-child(2) { transform: translate(-50%, -50%) rotate(90deg); }
        .attitude-marker:nth-child(3) { transform: translate(-50%, -50%) rotate(180deg); }
        .attitude-marker:nth-child(4) { transform: translate(-50%, -50%) rotate(270deg); }

        /* 控制按钮增强 */
        .control-buttons {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin-top: 20px;
        }

        .control-btn {
            background: linear-gradient(135deg, rgba(126, 211, 33, 0.1), rgba(15, 25, 40, 0.8));
            border: 1px solid rgba(126, 211, 33, 0.4);
            padding: 12px 8px;
            border-radius: 6px;
            color: #7ed321;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 11px;
            position: relative;
            overflow: hidden;
        }

        .control-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(126, 211, 33, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .control-btn:hover::before {
            left: 100%;
        }

        .control-btn:hover {
            background: linear-gradient(135deg, rgba(126, 211, 33, 0.2), rgba(15, 25, 40, 0.9));
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(126, 211, 33, 0.2);
        }

        .control-btn.active {
            background: linear-gradient(135deg, #7ed321, #9fff40);
            color: #000;
            box-shadow: 0 0 15px rgba(126, 211, 33, 0.5);
        }

        .control-btn i {
            display: block;
            font-size: 16px;
            margin-bottom: 4px;
        }

        /* 任务点网格增强 */
        .mission-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            margin-top: 15px;
        }

        .mission-point {
            background: linear-gradient(135deg, rgba(126, 211, 33, 0.1), rgba(15, 25, 40, 0.8));
            border: 1px solid rgba(126, 211, 33, 0.3);
            border-radius: 4px;
            padding: 8px;
            position: relative;
        }

        .mission-point-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 4px;
        }

        .mission-point-id {
            font-size: 10px;
            color: rgba(255, 255, 255, 0.7);
        }

        .mission-point-status {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #7ed321;
        }

        .mission-point-status.pending {
            background: #ffd700;
        }

        .mission-point-status.completed {
            background: #7ed321;
        }

        .mission-point-name {
            color: #7ed321;
            font-size: 11px;
            font-weight: bold;
        }

        .mission-point-details {
            font-size: 9px;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 2px;
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(15, 25, 40, 0.5);
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(126, 211, 33, 0.5);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(126, 211, 33, 0.7);
        }

        /* 响应式设计 */
        @media (max-width: 1400px) {
            .left-panel, .right-panel {
                width: 320px;
            }
        }

        @media (max-width: 1200px) {
            .left-panel, .right-panel {
                width: 280px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 左侧任务控制面板 -->
        <div class="left-panel">
            <div class="panel-header">
                <i class="fas fa-tasks"></i>
                <div class="panel-title">任务监控</div>
            </div>

            <div class="mission-section">
                <div class="mission-item">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-play-circle"></i>
                        <span>任务1</span>
            </div>
                    <div class="mission-status">
                        <i class="fas fa-clock"></i>
                        <span>进行中</span>
                        <div class="mission-progress">
                            <div class="mission-progress-fill" style="width: 75%"></div>
        </div>
                    </div>
                </div>
                <div class="mission-item">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-pause-circle"></i>
                        <span>任务2</span>
                    </div>
                    <div class="mission-status">
                        <i class="fas fa-pause"></i>
                        <span>待命</span>
                        <div class="mission-progress">
                            <div class="mission-progress-fill" style="width: 25%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="drone-list">
                <div class="drone-item">
                    <div class="drone-header">
                        <div class="drone-id-container">
                            <div class="drone-id">A1</div>
                            <div class="drone-signal">
                                <div class="signal-bar active"></div>
                                <div class="signal-bar active"></div>
                                <div class="signal-bar active"></div>
                                <div class="signal-bar"></div>
            </div>
            </div>
                        <i class="fas fa-helicopter" style="color: #7ed321;"></i>
                    </div>
                    <div class="drone-status-grid">
                        <div class="status-indicator">
                            <i class="fas fa-signal"></i>
                            <span>信号</span>
                        </div>
                        <div class="status-indicator">
                            <i class="fas fa-wifi"></i>
                            <span>传输</span>
                        </div>
                        <div class="status-indicator">
                            <i class="fas fa-thermometer-half"></i>
                            <span>红外</span>
                        </div>
                        <div class="status-indicator">
                            <i class="fas fa-eye"></i>
                            <span>可见</span>
                        </div>
                        <div class="status-indicator">
                            <i class="fas fa-satellite-dish"></i>
                            <span>GPS</span>
                        </div>
                        <div class="status-indicator">
                            <i class="fas fa-cog"></i>
                            <span>系统</span>
                        </div>
                    </div>
                    <div class="battery-container">
                        <i class="fas fa-battery-three-quarters battery-icon"></i>
                        <div class="battery-bar">
                            <div class="battery-fill" style="width: 95%"></div>
                        </div>
                        <span class="battery-percentage">95%</span>
                    </div>
                </div>

                <div class="drone-item">
                    <div class="drone-header">
                        <div class="drone-id-container">
                            <div class="drone-id">A2</div>
                            <div class="drone-signal">
                                <div class="signal-bar active"></div>
                                <div class="signal-bar active"></div>
                                <div class="signal-bar"></div>
                                <div class="signal-bar"></div>
                            </div>
                        </div>
                        <i class="fas fa-helicopter" style="color: #7ed321;"></i>
                    </div>
                    <div class="drone-status-grid">
                        <div class="status-indicator">
                            <i class="fas fa-signal"></i>
                            <span>信号</span>
                        </div>
                        <div class="status-indicator warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>传输</span>
                        </div>
                        <div class="status-indicator">
                            <i class="fas fa-thermometer-half"></i>
                            <span>红外</span>
                        </div>
                        <div class="status-indicator">
                            <i class="fas fa-eye"></i>
                            <span>可见</span>
                        </div>
                        <div class="status-indicator">
                            <i class="fas fa-satellite-dish"></i>
                            <span>GPS</span>
                        </div>
                        <div class="status-indicator">
                            <i class="fas fa-cog"></i>
                            <span>系统</span>
                        </div>
                    </div>
                    <div class="battery-container">
                        <i class="fas fa-battery-half battery-icon"></i>
                        <div class="battery-bar">
                            <div class="battery-fill" style="width: 85%"></div>
                        </div>
                        <span class="battery-percentage">85%</span>
                    </div>
                </div>

                <div class="drone-item">
                    <div class="drone-header">
                        <div class="drone-id-container">
                            <div class="drone-id">A3</div>
                            <div class="drone-signal">
                                <div class="signal-bar active"></div>
                                <div class="signal-bar"></div>
                                <div class="signal-bar"></div>
                                <div class="signal-bar"></div>
                            </div>
                        </div>
                        <i class="fas fa-helicopter" style="color: #ffd700;"></i>
                    </div>
                    <div class="drone-status-grid">
                        <div class="status-indicator error">
                            <i class="fas fa-times-circle"></i>
                            <span>信号</span>
                        </div>
                        <div class="status-indicator error">
                            <i class="fas fa-wifi"></i>
                            <span>传输</span>
                        </div>
                        <div class="status-indicator">
                            <i class="fas fa-thermometer-half"></i>
                            <span>红外</span>
                        </div>
                        <div class="status-indicator">
                            <i class="fas fa-eye"></i>
                            <span>可见</span>
                        </div>
                        <div class="status-indicator warning">
                            <i class="fas fa-satellite-dish"></i>
                            <span>GPS</span>
                        </div>
                        <div class="status-indicator">
                            <i class="fas fa-cog"></i>
                            <span>系统</span>
                        </div>
                    </div>
                    <div class="battery-container">
                        <i class="fas fa-battery-quarter battery-icon" style="color: #ffd700;"></i>
                        <div class="battery-bar">
                            <div class="battery-fill" style="width: 75%; background: linear-gradient(90deg, #ffd700, #ffed4e);"></div>
                        </div>
                        <span class="battery-percentage" style="color: #ffd700;">75%</span>
                    </div>
                    </div>
                </div>

            <div class="stats-section">
                <div class="panel-header">
                    <i class="fas fa-chart-line"></i>
                    <div class="panel-title">数据统计</div>
                </div>
                <div class="stats-chart">
                    <div class="chart-grid"></div>
                    <div class="chart-line">
                        <svg width="100%" height="100%">
                            <defs>
                                <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                    <stop offset="0%" style="stop-color:#7ed321;stop-opacity:0.8" />
                                    <stop offset="100%" style="stop-color:#7ed321;stop-opacity:0.1" />
                                </linearGradient>
                            </defs>
                            <path class="chart-area" d="M0,60 Q50,40 100,45 T200,35 T300,50 L300,80 L0,80 Z"/>
                            <path class="chart-path" d="M0,60 Q50,40 100,45 T200,35 T300,50"/>
                        </svg>
                    </div>
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span>任务完成率</span>
                        <div class="stat-value">
                            <i class="fas fa-check-circle"></i>
                            <span>95.0%</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <span>平台利用率</span>
                        <div class="stat-value">
                            <i class="fas fa-chart-pie"></i>
                            <span>98.0%</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <span>处理效率</span>
                        <div class="stat-value">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>85.0%</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <span>外观性能</span>
                        <div class="stat-value">
                            <i class="fas fa-exclamation-triangle" style="color: #ffd700;"></i>
                            <span>0</span>
                        </div>
                    </div>
                    </div>
                </div>
            </div>

        <!-- 中央地图区域 -->
        <div class="map-container">
            <!-- 真实地图显示 -->
            <div id="realMap"></div>
            
            <div class="map-overlay"></div>
            
            <div class="map-header">
                <div class="header-item">
                    <i class="fas fa-user-cog"></i>
                    <span>操作员A</span>
                </div>
                <div class="header-item">
                    <i class="fas fa-clock"></i>
                    <span class="time-info">CST 2025/02/29 11:28:08</span>
                </div>
                <div class="header-item">
                    <i class="fas fa-map"></i>
                    <span>OpenStreetMap</span>
                </div>
                <div class="header-item">
                    <div class="status-dot"></div>
                    <span>系统状态 • 正常</span>
                </div>
                <div class="header-item">
                    <i class="fas fa-satellite"></i>
                    <span>RTK差分</span>
                </div>
                <div class="header-item">
                    <i class="fas fa-wifi"></i>
                    <span>带宽: 58/100 Mbps</span>
                </div>
            </div>

            <!-- 无人机标记 -->
            <div class="drone-marker" style="top: 45%; left: 45%;">
                <div class="drone-radar"></div>
                <div class="drone-trail"></div>
                <div class="drone-icon-container">
                    <div class="drone-direction">
                        <i class="fas fa-location-arrow"></i>
                    </div>
                    <i class="fas fa-helicopter"></i>
                </div>
            </div>

            <!-- 任务区域标记 -->
            <div class="mission-marker" style="top: 35%; left: 60%;">
                <div class="mission-box">
                    <div class="mission-header">
                        <i class="fas fa-crosshairs"></i>
                        <span>任务区域</span>
                    </div>
                    <div class="mission-info">
                        <i class="fas fa-search"></i> 排查点-12:00:00 A.M<br>
                        <i class="fas fa-map-marker-alt"></i> 收集点 2km
                    </div>
                </div>
            </div>

            <div class="mission-marker" style="top: 65%; left: 75%;">
                <div class="mission-box">
                    <div class="mission-header">
                        <i class="fas fa-download"></i>
                        <span>收集区域</span>
                    </div>
                    <div class="mission-info">
                        <i class="fas fa-database"></i> 收集区-12:00:00 A.M<br>
                        <i class="fas fa-map-marker-alt"></i> 收集点 1.5km
                    </div>
                </div>
            </div>

            <!-- 无人机操控按钮组 -->
            <div class="drone-controls">
                <div class="drone-control-btn" title="起飞">
                    <i class="fas fa-rocket"></i>
                    <span class="btn-label">起飞</span>
                </div>
                <div class="drone-control-btn" title="降落">
                    <i class="fas fa-landmark"></i>
                    <span class="btn-label">降落</span>
                </div>
                <div class="drone-control-btn" title="悬停">
                    <i class="fas fa-pause-circle"></i>
                    <span class="btn-label">悬停</span>
                </div>
                <div class="drone-control-btn" title="返航">
                    <i class="fas fa-undo-alt"></i>
                    <span class="btn-label">返航</span>
                </div>
                <div class="drone-control-btn" title="紧急停止">
                    <i class="fas fa-hand-paper"></i>
                    <span class="btn-label">停止</span>
                </div>
                <div class="drone-control-btn" title="锁定">
                    <i class="fas fa-lock"></i>
                    <span class="btn-label">锁定</span>
                </div>
            </div>

            <!-- 地图控制按钮 -->
            <div class="map-controls">
                <div class="map-control-btn" title="放大" onclick="zoomIn()">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="map-control-btn" title="缩小" onclick="zoomOut()">
                    <i class="fas fa-minus"></i>
                </div>
                <div class="map-control-btn" title="定位" onclick="centerMap()">
                    <i class="fas fa-crosshairs"></i>
                </div>
                <div class="map-control-btn" title="图层" onclick="toggleLayer()">
                    <i class="fas fa-layer-group"></i>
                </div>
            </div>

            <!-- 地形图 -->
            <div class="terrain-map">
                <div class="terrain-header">
                    <i class="fas fa-mountain"></i>
                    <span>地形图 10km</span>
                </div>
                <svg width="100%" height="90px" style="padding: 0 15px;">
                    <defs>
                        <linearGradient id="terrainGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" style="stop-color:#7ed321;stop-opacity:0.8" />
                            <stop offset="100%" style="stop-color:#7ed321;stop-opacity:0.2" />
                        </linearGradient>
                    </defs>
                    <path d="M10,70 Q40,30 70,40 T130,25 T190,45 T210,30" 
                          stroke="#7ed321" 
                          stroke-width="2" 
                          fill="none"
                          filter="drop-shadow(0 0 3px rgba(126, 211, 33, 0.5))"/>
                    <path d="M10,70 Q40,30 70,40 T130,25 T190,45 T210,30 L210,70 L10,70 Z" 
                          fill="url(#terrainGradient)"/>
                        </svg>
                        </div>
                    </div>

        <!-- 右侧视频和控制面板 -->
        <div class="right-panel">
            <!-- 视频监控区域 -->
            <div class="video-section">
                <div class="video-header">
                    <div class="video-title">
                        <i class="fas fa-video"></i>
                        <span>单设备监控视频</span>
                    </div>
                    <div class="video-controls">
                        <div class="video-control-btn" title="录制">
                            <i class="fas fa-record-vinyl"></i>
                        </div>
                        <div class="video-control-btn" title="截图">
                            <i class="fas fa-camera"></i>
                        </div>
                        <div class="video-control-btn" title="全屏">
                            <i class="fas fa-expand"></i>
                        </div>
                        <div class="video-control-btn" title="设置">
                            <i class="fas fa-cog"></i>
                        </div>
                    </div>
                </div>
                <div class="video-grid">
                    <div class="video-feed main">
                        <div class="video-label">A1</div>
                        <div class="video-status">
                            <div class="status-light recording"></div>
                            <div class="status-light"></div>
                        </div>
                        <!-- 真实视频流 - 主视频 -->
                        <video 
                            autoplay 
                            muted 
                            loop 
                            style="width: 100%; height: 100%; object-fit: cover; background: #1a2332;"
                            poster="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 300'%3E%3Crect width='400' height='300' fill='%23333'/%3E%3Ctext x='200' y='150' text-anchor='middle' fill='%237ed321' font-size='16'%3E正在连接视频流...%3C/text%3E%3C/svg%3E">
                            <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
                            <source src="https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4" type="video/mp4">
                            您的浏览器不支持视频播放
                        </video>
                    </div>
                    <div class="video-feed">
                        <div class="video-label">A2</div>
                        <div class="video-status">
                            <div class="status-light"></div>
                        </div>
                        <!-- 第二个视频流 -->
                        <video 
                            autoplay 
                            muted 
                            loop 
                            style="width: 100%; height: 100%; object-fit: cover; background: #1a2332;"
                            poster="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 300'%3E%3Crect width='400' height='300' fill='%23333'/%3E%3Ctext x='200' y='150' text-anchor='middle' fill='%237ed321' font-size='12'%3E红外视频流%3C/text%3E%3C/svg%3E">
                            <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4" type="video/mp4">
                            <source src="https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4" type="video/mp4">
                            您的浏览器不支持视频播放
                        </video>
                    </div>
                    <div class="video-feed">
                        <div class="video-label">A3</div>
                        <div class="video-status">
                            <div class="status-light"></div>
                        </div>
                        <!-- 第三个视频流 -->
                        <video 
                            autoplay 
                            muted 
                            loop 
                            style="width: 100%; height: 100%; object-fit: cover; background: #1a2332;"
                            poster="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 300'%3E%3Crect width='400' height='300' fill='%23333'/%3E%3Ctext x='200' y='150' text-anchor='middle' fill='%237ed321' font-size='12'%3E可见光视频%3C/text%3E%3C/svg%3E">
                            <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4" type="video/mp4">
                            <source src="https://sample-videos.com/zip/10/mp4/SampleVideo_640x360_1mb.mp4" type="video/mp4">
                            您的浏览器不支持视频播放
                        </video>
                    </div>
                </div>
            </div>
            
            <!-- 控制面板 -->
            <div class="control-section">
                <div class="section-header">
                    <i class="fas fa-info-circle"></i>
                    <div class="section-title">单设备概览</div>
                </div>

                <div class="info-grid">
                    <div class="info-card">
                        <div class="info-card-header">
                            <i class="fas fa-building"></i>
                            <span class="info-label">01 建筑类型</span>
                </div>
                        <div class="info-value">
                            <span>20km</span>
                            <span style="margin-left: 8px;">巡逻系统</span>
            </div>
        </div>
                    <div class="info-card">
                        <div class="info-card-header">
                            <i class="fas fa-cogs"></i>
                            <span class="info-label">02 建筑设定</span>
                        </div>
                        <div class="info-value">
                            <span>20km</span>
                            <span style="margin-left: 8px;">进行中</span>
                        </div>
                    </div>
                </div>

                <!-- 姿态指示器 -->
                <div class="attitude-indicator">
                    <div class="attitude-markers">
                        <div class="attitude-marker"></div>
                        <div class="attitude-marker"></div>
                        <div class="attitude-marker"></div>
                        <div class="attitude-marker"></div>
                    </div>
                    <div class="attitude-inner">
                        <div style="text-align: center;">
                            <div style="font-size: 10px;">姿态角</div>
                            <div style="font-size: 16px;">0°</div>
                        </div>
                    </div>
                </div>

                <!-- 实时数据 -->
                <div>
                    <div class="section-header">
                        <i class="fas fa-map-marked-alt"></i>
                        <div class="section-title">单设备任务点</div>
                    </div>
                    <div class="mission-grid">
                        <div class="mission-point">
                            <div class="mission-point-header">
                                <span class="mission-point-id">次序</span>
                                <div class="mission-point-status completed"></div>
                            </div>
                            <div class="mission-point-name">各称</div>
                            <div class="mission-point-details">96.0%</div>
                        </div>
                        <div class="mission-point">
                            <div class="mission-point-header">
                                <span class="mission-point-id">预计到达</span>
                                <div class="mission-point-status completed"></div>
                            </div>
                            <div class="mission-point-name">状态</div>
                            <div class="mission-point-details">97.9%</div>
                        </div>
                        <div class="mission-point">
                            <div class="mission-point-header">
                                <span class="mission-point-id">01</span>
                                <div class="mission-point-status completed"></div>
                            </div>
                            <div class="mission-point-name">建筑类型</div>
                            <div class="mission-point-details">85.3%</div>
                        </div>
                        <div class="mission-point">
                            <div class="mission-point-header">
                                <span class="mission-point-id">SPT5°30'N</span>
                                <div class="mission-point-status pending"></div>
                            </div>
                            <div class="mission-point-name">已完成</div>
                            <div class="mission-point-details">进行中</div>
                        </div>
                    </div>
                </div>

                <!-- 控制按钮组 -->
                <div class="control-buttons">
                    <div class="control-btn">
                        <i class="fas fa-crosshairs"></i>
                        <span>定位</span>
                    </div>
                    <div class="control-btn active">
                        <i class="fas fa-play"></i>
                        <span>运行中</span>
                    </div>
                    <div class="control-btn">
                        <i class="fas fa-cog"></i>
                        <span>预设</span>
                    </div>
                    <div class="control-btn">
                        <i class="fas fa-info"></i>
                        <span>详情</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 地图相关变量
        let map;
        let droneMarker;
        let missionMarkers = [];
        let currentMapLayer = 'osm';
        
        // 添加动态效果和交互
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化地图
            initializeMap();
            
            // 更新时间
            function updateTime() {
                const now = new Date();
                const timeStr = now.toLocaleString('zh-CN', {
                    timeZone: 'Asia/Shanghai',
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
                const timeElement = document.querySelector('.time-info');
                if (timeElement) {
                    timeElement.textContent = `CST ${timeStr.replace(',', '')}`;
                }
            }

            // 初始化地图
            function initializeMap() {
                // 创建地图，设置中心点为北京
                map = L.map('realMap', {
                    center: [39.9042, 116.4074], // 北京坐标
                    zoom: 13,
                    zoomControl: false // 禁用默认缩放控件
                });

                // 添加OpenStreetMap图层
                const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/">OpenStreetMap</a>',
                    maxZoom: 19
                });

                // 添加卫星图层备选
                const satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                    attribution: '&copy; <a href="https://www.esri.com/">Esri</a>',
                    maxZoom: 19
                });

                // 默认使用OSM图层
                osmLayer.addTo(map);

                // 存储图层引用
                window.mapLayers = {
                    osm: osmLayer,
                    satellite: satelliteLayer
                };

                // 添加无人机标记
                addDroneMarker();
                
                // 添加任务区域标记
                addMissionMarkers();

                // 无人机实时移动
                startDroneMovement();
            }

            // 添加无人机标记
            function addDroneMarker() {
                // 创建自定义无人机图标
                const droneIcon = L.divIcon({
                    className: 'custom-drone-marker',
                    html: '<i class="fas fa-helicopter"></i>',
                    iconSize: [60, 60],
                    iconAnchor: [30, 30]
                });

                // 在地图上添加无人机标记
                droneMarker = L.marker([39.9042, 116.4074], {icon: droneIcon})
                    .addTo(map)
                    .bindPopup(`
                        <div style="color: #7ed321; font-weight: bold;">
                            <i class="fas fa-helicopter"></i> 无人机 A1<br>
                            <small>状态: 悬停中</small><br>
                            <small>高度: 120m</small><br>
                            <small>电量: 95%</small>
                        </div>
                    `);
            }

            // 添加任务区域标记
            function addMissionMarkers() {
                // 任务区域1
                const mission1 = L.marker([39.9100, 116.4200], {
                    icon: L.divIcon({
                        className: 'custom-mission-marker',
                        html: `
                            <div>
                                <i class="fas fa-crosshairs"></i> 任务区域<br>
                                <small>排查点-12:00:00 A.M</small><br>
                                <small>收集点 2km</small>
                            </div>
                        `,
                        iconSize: [140, 80],
                        iconAnchor: [70, 40]
                    })
                }).addTo(map);

                // 任务区域2
                const mission2 = L.marker([39.8980, 116.4300], {
                    icon: L.divIcon({
                        className: 'custom-mission-marker',
                        html: `
                            <div>
                                <i class="fas fa-download"></i> 收集区域<br>
                                <small>收集区-12:00:00 A.M</small><br>
                                <small>收集点 1.5km</small>
                            </div>
                        `,
                        iconSize: [140, 80],
                        iconAnchor: [70, 40]
                    })
                }).addTo(map);

                missionMarkers = [mission1, mission2];
            }

            // 无人机移动动画
            function startDroneMovement() {
                let moveIndex = 0;
                const movePoints = [
                    [39.9042, 116.4074],
                    [39.9060, 116.4090],
                    [39.9080, 116.4110],
                    [39.9070, 116.4130],
                    [39.9050, 116.4120],
                    [39.9042, 116.4074]
                ];

                setInterval(() => {
                    if (droneMarker) {
                        moveIndex = (moveIndex + 1) % movePoints.length;
                        droneMarker.setLatLng(movePoints[moveIndex]);
                    }
                }, 5000);
            }

            // 模拟无人机移动（保留原有的HTML标记动画）
            function animateDrone() {
                const drone = document.querySelector('.drone-marker');
                let x = 45;
                let y = 45;
                let dx = 0.3;
                let dy = 0.2;

                setInterval(() => {
                    x += dx;
                    y += dy;
                    
                    if (x > 80 || x < 20) dx = -dx;
                    if (y > 70 || y < 30) dy = -dy;
                    
                    drone.style.left = x + '%';
                    drone.style.top = y + '%';
                }, 3000);
            }

            // 模拟数据更新
            function updateStats() {
                const progressBars = document.querySelectorAll('.mission-progress-fill');
                const batteryBars = document.querySelectorAll('.battery-fill');
                const statValues = document.querySelectorAll('.stat-value span');
                
                setInterval(() => {
                    // 更新任务进度
                    progressBars.forEach(bar => {
                        const currentWidth = parseFloat(bar.style.width);
                        const variation = (Math.random() - 0.5) * 5;
                        const newWidth = Math.max(10, Math.min(100, currentWidth + variation));
                        bar.style.width = newWidth + '%';
                    });

                    // 更新统计数据
                    statValues.forEach((span, index) => {
                        if (span.textContent.includes('%')) {
                            const baseValue = parseFloat(span.textContent);
                            const variation = (Math.random() - 0.5) * 3;
                            const newValue = Math.max(0, Math.min(100, baseValue + variation));
                            span.textContent = newValue.toFixed(1) + '%';
                        }
                    });
                }, 4000);
            }

            // 信号强度动画
            function animateSignalBars() {
                const signalBars = document.querySelectorAll('.signal-bar');
                setInterval(() => {
                    signalBars.forEach(bar => {
                        if (Math.random() > 0.7) {
                            bar.classList.toggle('active');
                        }
                    });
                }, 1500);
            }

            // 控制按钮交互
            function initControlButtons() {
                document.querySelectorAll('.control-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        document.querySelectorAll('.control-btn').forEach(b => b.classList.remove('active'));
                        this.classList.add('active');
                    });
                });

                // 地图控制按钮
                document.querySelectorAll('.map-control-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        this.style.background = 'rgba(126, 211, 33, 0.4)';
                        this.style.transform = 'translateY(-2px) scale(0.95)';
                        setTimeout(() => {
                            this.style.background = '';
                            this.style.transform = '';
                        }, 200);
                    });
                });

                // 视频控制按钮
                document.querySelectorAll('.video-control-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        this.style.background = 'rgba(126, 211, 33, 0.3)';
                        setTimeout(() => {
                            this.style.background = '';
                        }, 200);
                    });
                });

                // 无人机操控按钮
                initDroneControlButtons();
            }

            // 无人机操控按钮交互
            function initDroneControlButtons() {
                const droneControlBtns = document.querySelectorAll('.drone-control-btn');
                let currentMode = null;

                droneControlBtns.forEach((btn, index) => {
                    // 添加状态指示器
                    const indicator = document.createElement('div');
                    indicator.className = 'control-status-indicator';
                    btn.appendChild(indicator);

                    btn.addEventListener('click', function() {
                        const btnText = this.querySelector('.btn-label').textContent;
                        
                        // 处理不同的操控命令
                        switch(btnText) {
                            case '起飞':
                                handleTakeoff(this);
                                break;
                            case '降落':
                                handleLanding(this);
                                break;
                            case '悬停':
                                handleHover(this);
                                break;
                            case '返航':
                                handleReturnHome(this);
                                break;
                            case '停止':
                                handleEmergencyStop(this);
                                break;
                            case '锁定':
                                handleLock(this);
                                break;
                        }
                    });
                });

                // 操控命令处理函数
                function handleTakeoff(btn) {
                    if (currentMode === 'flying') return;
                    
                    setActiveButton(btn);
                    currentMode = 'flying';
                    simulateDroneAction('起飞中...', 2000, () => {
                        currentMode = 'hovering';
                        updateDroneStatus('悬停中');
                    });
                }

                function handleLanding(btn) {
                    if (currentMode === 'grounded') return;
                    
                    setActiveButton(btn);
                    simulateDroneAction('降落中...', 3000, () => {
                        currentMode = 'grounded';
                        clearActiveButtons();
                        updateDroneStatus('已着陆');
                    });
                }

                function handleHover(btn) {
                    if (currentMode === 'grounded') return;
                    
                    setActiveButton(btn);
                    currentMode = 'hovering';
                    updateDroneStatus('悬停中');
                }

                function handleReturnHome(btn) {
                    if (currentMode === 'grounded') return;
                    
                    setActiveButton(btn);
                    currentMode = 'returning';
                    simulateDroneAction('返航中...', 5000, () => {
                        currentMode = 'grounded';
                        clearActiveButtons();
                        updateDroneStatus('返航完成');
                        
                        // 地图上无人机返回起始位置
                        if (droneMarker) {
                            droneMarker.setLatLng([39.9042, 116.4074]);
                        }
                    });
                }

                function handleEmergencyStop(btn) {
                    btn.classList.add('emergency');
                    setActiveButton(btn);
                    currentMode = 'emergency';
                    updateDroneStatus('紧急停止');
                    
                    // 紧急停止后2秒自动解除
                    setTimeout(() => {
                        btn.classList.remove('emergency');
                        currentMode = 'hovering';
                        updateDroneStatus('等待指令');
                    }, 2000);
                }

                function handleLock(btn) {
                    const isLocked = btn.classList.contains('active');
                    if (isLocked) {
                        btn.classList.remove('active');
                        updateDroneStatus('解锁成功');
                    } else {
                        setActiveButton(btn);
                        updateDroneStatus('已锁定');
                    }
                }

                function setActiveButton(activeBtn) {
                    clearActiveButtons();
                    activeBtn.classList.add('active');
                }

                function clearActiveButtons() {
                    droneControlBtns.forEach(btn => {
                        btn.classList.remove('active');
                        btn.classList.remove('emergency');
                    });
                }

                function simulateDroneAction(message, duration, callback) {
                    updateDroneStatus(message);
                    setTimeout(callback, duration);
                }

                function updateDroneStatus(status) {
                    // 更新状态显示（可以在界面上添加状态显示区域）
                    console.log('无人机状态:', status);
                    
                    // 创建状态提示
                    showStatusToast(status);
                    
                    // 更新地图上的无人机弹窗信息
                    if (droneMarker) {
                        droneMarker.setPopupContent(`
                            <div style="color: #7ed321; font-weight: bold;">
                                <i class="fas fa-helicopter"></i> 无人机 A1<br>
                                <small>状态: ${status}</small><br>
                                <small>高度: 120m</small><br>
                                <small>电量: 95%</small>
                            </div>
                        `);
                    }
                }

                function showStatusToast(message) {
                    // 移除现有的提示
                    const existingToast = document.querySelector('.status-toast');
                    if (existingToast) {
                        existingToast.remove();
                    }

                    // 创建新的状态提示
                    const toast = document.createElement('div');
                    toast.className = 'status-toast';
                    toast.style.cssText = `
                        position: fixed;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        background: rgba(15, 25, 40, 0.95);
                        color: #7ed321;
                        padding: 12px 24px;
                        border-radius: 8px;
                        border: 1px solid rgba(126, 211, 33, 0.5);
                        font-size: 14px;
                        font-weight: bold;
                        z-index: 1000;
                        animation: toastFadeIn 0.3s ease;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                    `;
                    toast.textContent = message;
                    document.body.appendChild(toast);

                    // 3秒后自动消失
                    setTimeout(() => {
                        toast.style.animation = 'toastFadeOut 0.3s ease';
                        setTimeout(() => toast.remove(), 300);
                    }, 2000);
                }

                // 添加动画样式
                const style = document.createElement('style');
                style.textContent = `
                    @keyframes toastFadeIn {
                        from { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                        to { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                    }
                    @keyframes toastFadeOut {
                        from { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                        to { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                    }
                `;
                document.head.appendChild(style);
            }

            // 姿态仪表随机旋转
            function animateAttitudeIndicator() {
                const inner = document.querySelector('.attitude-inner');
                let currentRotation = 0;
                
                setInterval(() => {
                    const variation = (Math.random() - 0.5) * 20;
                    currentRotation += variation;
                    inner.style.transform = `rotate(${currentRotation}deg)`;
                    
                    // 更新角度显示
                    const angleDisplay = inner.querySelector('div:last-child');
                    angleDisplay.textContent = Math.round(currentRotation % 360) + '°';
                }, 2000);
            }

            // 任务点状态更新
            function updateMissionPoints() {
                const statusDots = document.querySelectorAll('.mission-point-status');
                const percentages = document.querySelectorAll('.mission-point-details');
                
                setInterval(() => {
                    percentages.forEach(percentage => {
                        if (percentage.textContent.includes('%')) {
                            const baseValue = parseFloat(percentage.textContent);
                            const variation = (Math.random() - 0.5) * 2;
                            const newValue = Math.max(0, Math.min(100, baseValue + variation));
                            percentage.textContent = newValue.toFixed(1) + '%';
                        }
                    });
                }, 3000);
            }

            // 初始化所有功能
            updateTime();
            setInterval(updateTime, 1000);
            animateDrone();
            updateStats();
            animateSignalBars();
            initControlButtons();
            animateAttitudeIndicator();
            updateMissionPoints();
            
            // 初始化视频流
            initVideoStreams();

            // 初始化视频流
            function initVideoStreams() {
                const videos = document.querySelectorAll('.video-feed video');
                
                videos.forEach((video, index) => {
                    // 视频加载成功
                    video.addEventListener('loadstart', function() {
                        console.log(`视频流 ${index + 1} 开始加载`);
                    });

                    video.addEventListener('canplay', function() {
                        console.log(`视频流 ${index + 1} 可以播放`);
                        // 更新状态指示灯
                        const statusLight = this.parentElement.querySelector('.status-light');
                        if (statusLight) {
                            statusLight.style.background = '#7ed321';
                        }
                    });

                    // 视频加载失败时的处理
                    video.addEventListener('error', function() {
                        console.log(`视频流 ${index + 1} 加载失败，尝试备用源`);
                        // 显示错误状态
                        const statusLight = this.parentElement.querySelector('.status-light');
                        if (statusLight) {
                            statusLight.style.background = '#ff6b47';
                        }
                        
                        // 显示备用内容
                        this.style.display = 'none';
                        const fallbackDiv = document.createElement('div');
                        fallbackDiv.innerHTML = `
                            <div style="width: 100%; height: 100%; background: linear-gradient(45deg, #1a2332, #2a3442); 
                                        display: flex; align-items: center; justify-content: center; flex-direction: column;">
                                <i class="fas fa-exclamation-triangle" style="color: #ffd700; font-size: 24px; margin-bottom: 10px;"></i>
                                <div style="color: #7ed321; font-size: 12px; text-align: center;">
                                    视频流连接中断<br>
                                    <small style="color: rgba(255,255,255,0.7);">正在尝试重连...</small>
                                </div>
                            </div>
                        `;
                        this.parentElement.appendChild(fallbackDiv);
                        
                        // 5秒后尝试重连
                        setTimeout(() => {
                            this.load();
                            fallbackDiv.remove();
                            this.style.display = 'block';
                        }, 5000);
                    });

                    // 视频暂停时自动重播
                    video.addEventListener('ended', function() {
                        this.currentTime = 0;
                        this.play();
                    });

                    // 确保视频自动播放
                    video.addEventListener('loadeddata', function() {
                        this.play().catch(e => {
                            console.log('自动播放被阻止，需要用户交互');
                        });
                    });
                });

                // 视频控制按钮功能增强
                document.querySelectorAll('.video-control-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const title = this.getAttribute('title');
                        const mainVideo = document.querySelector('.video-feed.main video');
                        
                        switch(title) {
                            case '录制':
                                toggleRecording(this);
                                break;
                            case '截图':
                                takeScreenshot(mainVideo);
                                break;
                            case '全屏':
                                toggleFullscreen(mainVideo);
                                break;
                            case '设置':
                                showVideoSettings();
                                break;
                        }
                    });
                });

                function toggleRecording(btn) {
                    const isRecording = btn.classList.contains('recording');
                    if (isRecording) {
                        btn.classList.remove('recording');
                        btn.style.color = '#7ed321';
                        showStatusToast('录制已停止');
                    } else {
                        btn.classList.add('recording');
                        btn.style.color = '#ff3333';
                        showStatusToast('开始录制视频');
                    }
                }

                function takeScreenshot(video) {
                    const canvas = document.createElement('canvas');
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(video, 0, 0);
                    
                    // 创建下载链接
                    const link = document.createElement('a');
                    link.download = `drone_screenshot_${new Date().getTime()}.png`;
                    link.href = canvas.toDataURL();
                    link.click();
                    
                    showStatusToast('截图已保存');
                }

                function toggleFullscreen(video) {
                    if (document.fullscreenElement) {
                        document.exitFullscreen();
                    } else {
                        video.requestFullscreen().catch(err => {
                            showStatusToast('全屏功能不可用');
                        });
                    }
                }

                function showVideoSettings() {
                    showStatusToast('视频设置面板');
                }
            }

            // 添加键盘快捷键
            document.addEventListener('keydown', function(e) {
                switch(e.key) {
                    case '1':
                        document.querySelector('.control-btn:nth-child(1)').click();
                        break;
                    case '2':
                        document.querySelector('.control-btn:nth-child(2)').click();
                        break;
                    case '3':
                        document.querySelector('.control-btn:nth-child(3)').click();
                        break;
                    case '4':
                        document.querySelector('.control-btn:nth-child(4)').click();
                        break;
                }
            });
        });

        // 地图控制函数（全局函数，供HTML onclick调用）
        function zoomIn() {
            if (map) {
                map.zoomIn();
            }
        }

        function zoomOut() {
            if (map) {
                map.zoomOut();
            }
        }

        function centerMap() {
            if (map && droneMarker) {
                map.setView(droneMarker.getLatLng(), 15);
            }
        }

        function toggleLayer() {
            if (map && window.mapLayers) {
                if (currentMapLayer === 'osm') {
                    map.removeLayer(window.mapLayers.osm);
                    map.addLayer(window.mapLayers.satellite);
                    currentMapLayer = 'satellite';
                    document.querySelector('.header-item:nth-child(3) span').textContent = '卫星地图';
                } else {
                    map.removeLayer(window.mapLayers.satellite);
                    map.addLayer(window.mapLayers.osm);
                    currentMapLayer = 'osm';
                    document.querySelector('.header-item:nth-child(3) span').textContent = 'OpenStreetMap';
                }
            }
        }
    </script>
</body>
</html> 