<template>
  <div class="left-tab-panel">
    <!-- 选项卡导航 -->
    <div class="tab-navigation">
      <div
        v-for="tab in tabs"
        :key="tab.id"
        class="tab-item"
        :class="{ active: selectedTabs.includes(tab.id) }"
        @click="toggleTab(tab.id)"
      >
        <i :class="tab.icon"></i>
        <span>{{ tab.name }}</span>
      </div>
    </div>

    <!-- 选项卡内容区域 -->
    <div class="tab-content-area">
      <!-- 任务监控面板 -->
      <div v-if="selectedTabs.includes('missions')" class="tab-panel missions-panel">
        <div class="panel-header">
          <i class="fas fa-tasks"></i>
          <div class="panel-title">任务监控</div>
          <i class="fas fa-times close-btn" @click="removeTab('missions')"></i>
        </div>
        
        <div class="mission-section">
          <div 
            v-for="mission in missions" 
            :key="mission.id"
            class="mission-item"
          >
            <div style="display: flex; align-items: center; gap: 8px;">
              <i :class="mission.icon"></i>
              <span>{{ mission.name }}</span>
            </div>
            <div class="mission-status">
              <i :class="mission.statusIcon"></i>
              <span>{{ mission.status }}</span>
              <div class="mission-progress">
                <div 
                  class="mission-progress-fill" 
                  :style="{ width: mission.progress + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 无人机列表 -->
        <div class="drone-list">
          <div 
            v-for="drone in drones" 
            :key="drone.id"
            class="drone-item"
          >
            <div class="drone-header">
              <div class="drone-id-container">
                <div class="drone-id">{{ drone.id }}</div>
                <div class="drone-signal">
                  <div 
                    v-for="(bar, index) in 4" 
                    :key="index"
                    class="signal-bar"
                    :class="{ active: index < drone.signalStrength }"
                  ></div>
                </div>
              </div>
              <i class="fas fa-helicopter" :style="{ color: drone.statusColor }"></i>
            </div>
            
            <div class="drone-status-grid">
              <div 
                v-for="status in drone.statusItems" 
                :key="status.name"
                class="status-indicator"
                :class="status.type"
              >
                <i :class="status.icon"></i>
                <span>{{ status.name }}</span>
              </div>
            </div>
            
            <div class="battery-container">
              <i :class="drone.batteryIcon" class="battery-icon" :style="{ color: drone.batteryColor }"></i>
              <div class="battery-bar">
                <div 
                  class="battery-fill" 
                  :style="{ 
                    width: drone.battery + '%',
                    background: drone.batteryGradient 
                  }"
                ></div>
              </div>
              <span class="battery-percentage" :style="{ color: drone.batteryColor }">{{ drone.battery }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据统计面板 -->
      <div v-if="selectedTabs.includes('statistics')" class="tab-panel statistics-panel">
        <div class="panel-header">
          <i class="fas fa-chart-line"></i>
          <div class="panel-title">数据统计</div>
          <i class="fas fa-times close-btn" @click="removeTab('statistics')"></i>
        </div>
        
        <div class="stats-chart">
          <div class="chart-grid"></div>
          <div class="chart-line">
            <svg width="100%" height="100%">
              <defs>
                <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" style="stop-color:#7ed321;stop-opacity:0.8" />
                  <stop offset="100%" style="stop-color:#7ed321;stop-opacity:0.1" />
                </linearGradient>
              </defs>
              <path class="chart-area" d="M0,60 Q50,40 100,45 T200,35 T300,50 L300,80 L0,80 Z"/>
              <path class="chart-path" d="M0,60 Q50,40 100,45 T200,35 T300,50"/>
            </svg>
          </div>
        </div>
        
        <div class="stats-grid">
          <div 
            v-for="stat in stats" 
            :key="stat.name"
            class="stat-item"
          >
            <span>{{ stat.name }}</span>
            <div class="stat-value">
              <i :class="stat.icon" :style="{ color: stat.color }"></i>
              <span>{{ stat.value }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 设备概览面板 -->
      <div v-if="selectedTabs.includes('overview')" class="tab-panel overview-panel">
        <div class="panel-header">
          <i class="fas fa-info-circle"></i>
          <div class="panel-title">设备概览</div>
          <i class="fas fa-times close-btn" @click="removeTab('overview')"></i>
        </div>
        
        <div class="info-grid">
          <div
            v-for="info in deviceInfo"
            :key="info.label"
            class="info-card"
          >
            <div class="info-card-header">
              <i :class="info.icon"></i>
              <span class="info-number">{{ info.number }}</span>
              <span class="info-label">{{ info.label }}</span>
            </div>
            <div class="info-details">
              <div class="info-value">{{ info.value }}</div>
              <div class="info-status">{{ info.status }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 飞行姿态面板 -->
      <div v-if="selectedTabs.includes('attitude')" class="tab-panel attitude-panel">
        <div class="panel-header">
          <i class="fas fa-compass"></i>
          <div class="panel-title">飞行姿态</div>
          <i class="fas fa-times close-btn" @click="removeTab('attitude')"></i>
        </div>
        
        <div class="attitude-indicator">
          <div class="attitude-markers">
            <div
              v-for="n in 4"
              :key="n"
              class="attitude-marker"
            ></div>
          </div>
          <div class="attitude-inner" :style="{ transform: `rotate(${attitudeAngle}deg)` }">
            <div style="text-align: center;">
              <div style="font-size: 10px;">姿态角</div>
              <div style="font-size: 16px;">{{ Math.round(attitudeAngle % 360) }}°</div>
            </div>
          </div>
        </div>

        <div class="attitude-data">
          <div class="attitude-item">
            <span class="label">俯仰角</span>
            <span class="value">{{ attitudeData.pitch.toFixed(1) }}°</span>
          </div>
          <div class="attitude-item">
            <span class="label">横滚角</span>
            <span class="value">{{ attitudeData.roll.toFixed(1) }}°</span>
          </div>
          <div class="attitude-item">
            <span class="label">航向角</span>
            <span class="value">{{ attitudeData.yaw.toFixed(1) }}°</span>
          </div>
        </div>
      </div>

      <!-- 任务点面板 -->
      <div v-if="selectedTabs.includes('mission')" class="tab-panel mission-panel">
        <div class="panel-header">
          <i class="fas fa-map-marked-alt"></i>
          <div class="panel-title">任务点</div>
          <i class="fas fa-times close-btn" @click="removeTab('mission')"></i>
        </div>
        
        <div class="mission-grid">
          <div
            v-for="point in missionPoints"
            :key="point.id"
            class="mission-point"
          >
            <div class="mission-point-header">
              <span class="mission-point-id">{{ point.id }}</span>
              <div
                class="mission-point-status"
                :class="point.status"
              ></div>
            </div>
            <div class="mission-point-name">{{ point.name }}</div>
            <div class="mission-point-details">{{ point.details }}</div>
          </div>
        </div>
      </div>

      <!-- 设备控制面板 -->
      <div v-if="selectedTabs.includes('control')" class="tab-panel control-panel">
        <div class="panel-header">
          <i class="fas fa-gamepad"></i>
          <div class="panel-title">设备控制</div>
          <i class="fas fa-times close-btn" @click="removeTab('control')"></i>
        </div>
        
        <div class="control-buttons">
          <div
            v-for="control in controlButtons"
            :key="control.name"
            class="control-btn"
            :class="{ active: control.active }"
            @click="handleControlButton(control)"
          >
            <i :class="control.icon"></i>
            <span>{{ control.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LeftTabPanel',
  data() {
    return {
      // 选项卡配置
      selectedTabs: ['missions'], // 默认显示任务监控
      tabs: [
        { id: 'missions', name: '任务监控', icon: 'fas fa-tasks' },
        { id: 'statistics', name: '数据统计', icon: 'fas fa-chart-line' },
        { id: 'overview', name: '设备概览', icon: 'fas fa-info-circle' },
        { id: 'attitude', name: '飞行姿态', icon: 'fas fa-compass' },
        { id: 'mission', name: '任务点', icon: 'fas fa-map-marked-alt' },
        { id: 'control', name: '设备控制', icon: 'fas fa-gamepad' }
      ],
      // 任务监控数据
      missions: [
        {
          id: 1,
          name: '任务1',
          icon: 'fas fa-play-circle',
          status: '进行中',
          statusIcon: 'fas fa-clock',
          progress: 75
        },
        {
          id: 2,
          name: '任务2',
          icon: 'fas fa-pause-circle',
          status: '待命',
          statusIcon: 'fas fa-pause',
          progress: 25
        }
      ],
      // 无人机数据
      drones: [
        {
          id: 'A1',
          signalStrength: 3,
          statusColor: '#7ed321',
          battery: 95,
          batteryIcon: 'fas fa-battery-three-quarters',
          batteryColor: '#7ed321',
          batteryGradient: 'linear-gradient(90deg, #7ed321, #9fff40)',
          statusItems: [
            { name: '信号', icon: 'fas fa-signal', type: '' },
            { name: '传输', icon: 'fas fa-wifi', type: '' },
            { name: '红外', icon: 'fas fa-thermometer-half', type: '' },
            { name: '可见', icon: 'fas fa-eye', type: '' },
            { name: 'GPS', icon: 'fas fa-satellite-dish', type: '' },
            { name: '系统', icon: 'fas fa-cog', type: '' }
          ]
        },
        {
          id: 'A2',
          signalStrength: 2,
          statusColor: '#ffd700',
          battery: 68,
          batteryIcon: 'fas fa-battery-half',
          batteryColor: '#ffd700',
          batteryGradient: 'linear-gradient(90deg, #ffd700, #ffed4e)',
          statusItems: [
            { name: '信号', icon: 'fas fa-signal', type: '' },
            { name: '传输', icon: 'fas fa-wifi', type: '' },
            { name: '红外', icon: 'fas fa-thermometer-half', type: '' },
            { name: '可见', icon: 'fas fa-eye', type: '' },
            { name: 'GPS', icon: 'fas fa-satellite-dish', type: '' },
            { name: '系统', icon: 'fas fa-cog', type: '' }
          ]
        }
      ],
      // 统计数据
      stats: [
        { name: '任务完成率', icon: 'fas fa-check-circle', color: '#7ed321', value: '95.0%' },
        { name: '平台利用率', icon: 'fas fa-chart-pie', color: '#7ed321', value: '98.0%' },
        { name: '处理效率', icon: 'fas fa-tachometer-alt', color: '#7ed321', value: '85.0%' },
        { name: '外观性能', icon: 'fas fa-exclamation-triangle', color: '#ffd700', value: '0' }
      ],
      // 设备信息
      deviceInfo: [
        {
          number: '01',
          label: '建筑类型',
          icon: 'fas fa-building',
          value: '20km',
          status: '巡逻系统'
        },
        {
          number: '02',
          label: '建筑设定',
          icon: 'fas fa-cogs',
          value: '20km',
          status: '进行中'
        }
      ],
      // 姿态数据
      attitudeAngle: 0,
      attitudeData: {
        pitch: 2.5,
        roll: -1.2,
        yaw: 45.8
      },
      // 任务点数据
      missionPoints: [
        { id: '次序', name: '各称', details: '96.0%', status: 'completed' },
        { id: '预计到达', name: '状态', details: '97.9%', status: 'completed' },
        { id: '01', name: '建筑类型', details: '85.3%', status: 'completed' },
        { id: 'SPT5°30\'N', name: '已完成', details: '进行中', status: 'pending' }
      ],
      // 控制按钮
      controlButtons: [
        { name: '定位', icon: 'fas fa-crosshairs', active: false },
        { name: '运行中', icon: 'fas fa-play', active: true },
        { name: '预设', icon: 'fas fa-cog', active: false },
        { name: '详情', icon: 'fas fa-info', active: false }
      ]
    }
  },
  mounted() {
    this.startAnimations()
  },
  methods: {
    // 选项卡管理方法
    toggleTab(tabId) {
      const index = this.selectedTabs.indexOf(tabId)
      if (index > -1) {
        this.selectedTabs.splice(index, 1)
      } else {
        this.selectedTabs.push(tabId)
      }
    },
    removeTab(tabId) {
      const index = this.selectedTabs.indexOf(tabId)
      if (index > -1) {
        this.selectedTabs.splice(index, 1)
      }
    },
    // 动画和数据更新
    startAnimations() {
      // 姿态仪表随机旋转
      setInterval(() => {
        const variation = (Math.random() - 0.5) * 20
        this.attitudeAngle += variation
      }, 2000)

      // 姿态数据更新
      setInterval(() => {
        this.attitudeData.pitch += (Math.random() - 0.5) * 2
        this.attitudeData.roll += (Math.random() - 0.5) * 2
        this.attitudeData.yaw += (Math.random() - 0.5) * 5

        // 限制范围
        this.attitudeData.pitch = Math.max(-90, Math.min(90, this.attitudeData.pitch))
        this.attitudeData.roll = Math.max(-180, Math.min(180, this.attitudeData.roll))
        this.attitudeData.yaw = ((this.attitudeData.yaw % 360) + 360) % 360
      }, 1500)

      // 任务点数据更新
      setInterval(() => {
        this.missionPoints.forEach(point => {
          if (point.details.includes('%')) {
            const baseValue = parseFloat(point.details)
            const variation = (Math.random() - 0.5) * 2
            const newValue = Math.max(0, Math.min(100, baseValue + variation))
            point.details = newValue.toFixed(1) + '%'
          }
        })
      }, 3000)

      // 模拟数据更新
      setInterval(() => {
        this.updateMissionProgress()
        this.updateStats()
        this.updateSignalBars()
      }, 4000)
    },
    updateMissionProgress() {
      this.missions.forEach(mission => {
        const variation = (Math.random() - 0.5) * 5
        mission.progress = Math.max(10, Math.min(100, mission.progress + variation))
      })
    },
    updateStats() {
      this.stats.forEach(stat => {
        if (stat.value.includes('%')) {
          const baseValue = parseFloat(stat.value)
          const variation = (Math.random() - 0.5) * 3
          const newValue = Math.max(0, Math.min(100, baseValue + variation))
          stat.value = newValue.toFixed(1) + '%'
        }
      })
    },
    updateSignalBars() {
      this.drones.forEach(drone => {
        if (Math.random() > 0.7) {
          drone.signalStrength = Math.floor(Math.random() * 4) + 1
        }
      })
    },
    handleControlButton(control) {
      // 清除其他按钮状态（除了运行中）
      if (control.name !== '运行中') {
        this.controlButtons.forEach(btn => {
          if (btn !== control && btn.name !== '运行中') {
            btn.active = false
          }
        })
      }

      control.active = !control.active

      switch(control.name) {
        case '定位':
          this.handlePositioning(control.active)
          break
        case '运行中':
          this.handleRunning(control.active)
          break
        case '预设':
          this.handlePreset(control.active)
          break
        case '详情':
          this.handleDetails(control.active)
          break
      }
    },
    handlePositioning(active) {
      console.log(active ? '开始定位' : '停止定位')
    },
    handleRunning(active) {
      console.log(active ? '系统运行中' : '系统已停止')
    },
    handlePreset(active) {
      console.log(active ? '显示预设' : '隐藏预设')
    },
    handleDetails(active) {
      console.log(active ? '显示详情' : '隐藏详情')
    }
  }
}
</script>

<style scoped>
/* ================================ 左侧选项卡面板 ================================ */
.left-tab-panel {
  width: 320px;
  background: rgba(15, 25, 40, 0.95);
  border-right: 1px solid rgba(126, 211, 33, 0.3);
  display: flex;
  flex-direction: column;
  height: 100vh;
  box-shadow: inset -1px 0 10px rgba(126, 211, 33, 0.1);
}

/* 选项卡导航 */
.tab-navigation {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 16px;
  border-bottom: 1px solid rgba(126, 211, 33, 0.3);
  background: rgba(26, 35, 50, 0.5);
}

.tab-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(126, 211, 33, 0.1);
  border: 1px solid rgba(126, 211, 33, 0.3);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
  justify-content: center;
}

.tab-item:hover {
  background: rgba(126, 211, 33, 0.2);
  color: rgba(255, 255, 255, 0.9);
  border-color: rgba(126, 211, 33, 0.5);
  transform: translateY(-1px);
}

.tab-item.active {
  background: linear-gradient(135deg, rgba(126, 211, 33, 0.3), rgba(126, 211, 33, 0.2));
  color: #7ed321;
  border-color: #7ed321;
  box-shadow: 0 2px 8px rgba(126, 211, 33, 0.3);
}

.tab-item i {
  font-size: 14px;
}

/* 选项卡内容区域 */
.tab-content-area {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.tab-panel {
  background: linear-gradient(180deg, rgba(26, 35, 50, 0.8) 0%, rgba(15, 20, 25, 0.8) 100%);
  border: 1px solid rgba(126, 211, 33, 0.3);
  border-radius: 8px;
  margin-bottom: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(126, 211, 33, 0.2);
  background: rgba(126, 211, 33, 0.05);
}

.panel-header i:first-child {
  color: #7ed321;
  font-size: 16px;
}

.panel-title {
  color: #7ed321;
  font-size: 14px;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(126, 211, 33, 0.5);
  flex: 1;
  margin-left: 10px;
}

.close-btn {
  cursor: pointer;
  color: rgba(255, 255, 255, 0.6);
  transition: color 0.3s ease;
  font-size: 14px;
}

.close-btn:hover {
  color: #ff6b6b;
}

/* 任务监控样式 */
.mission-section {
  padding: 16px;
  border-bottom: 1px solid rgba(126, 211, 33, 0.2);
}

.mission-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 10px;
  background: rgba(126, 211, 33, 0.05);
  border: 1px solid rgba(126, 211, 33, 0.2);
  border-radius: 6px;
  color: #ffffff;
  font-size: 13px;
  transition: all 0.3s ease;
}

.mission-item:hover {
  background: rgba(126, 211, 33, 0.1);
  border-color: rgba(126, 211, 33, 0.4);
  transform: translateY(-1px);
}

.mission-status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #7ed321;
  font-size: 11px;
}

.mission-progress {
  width: 60px;
  height: 4px;
  background: rgba(126, 211, 33, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.mission-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #7ed321, #9fff40);
  border-radius: 2px;
  transition: width 0.5s ease;
}

/* 无人机列表样式 */
.drone-list {
  padding: 16px;
}

.drone-item {
  background: rgba(126, 211, 33, 0.05);
  border: 1px solid rgba(126, 211, 33, 0.3);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.drone-item:hover {
  background: rgba(126, 211, 33, 0.1);
  border-color: rgba(126, 211, 33, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(126, 211, 33, 0.2);
}

.drone-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.drone-id-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.drone-id {
  color: #000;
  background: linear-gradient(135deg, #7ed321, #9fff40);
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: bold;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(126, 211, 33, 0.3);
}

.drone-signal {
  display: flex;
  gap: 2px;
}

.signal-bar {
  width: 3px;
  height: 12px;
  background: rgba(126, 211, 33, 0.3);
  border-radius: 1px;
}

.signal-bar.active {
  background: #7ed321;
  animation: signal-pulse 1s ease-in-out infinite alternate;
}

@keyframes signal-pulse {
  from { opacity: 0.7; }
  to { opacity: 1; }
}

.drone-status-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-bottom: 10px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
}

.status-indicator i {
  color: #7ed321;
  font-size: 12px;
}

.battery-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.battery-icon {
  font-size: 16px;
}

.battery-bar {
  flex: 1;
  height: 8px;
  background: rgba(126, 211, 33, 0.2);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.battery-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease;
  position: relative;
}

.battery-fill::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
  animation: battery-shine 2s ease-in-out infinite;
}

@keyframes battery-shine {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

.battery-percentage {
  font-size: 11px;
  color: #7ed321;
  font-weight: bold;
  min-width: 35px;
}

/* 数据统计样式 */
.stats-chart {
  background: linear-gradient(135deg, rgba(126, 211, 33, 0.1), rgba(15, 25, 40, 0.8));
  height: 140px;
  border-radius: 8px;
  border: 1px solid rgba(126, 211, 33, 0.4);
  position: relative;
  overflow: hidden;
  margin: 16px;
}

.chart-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
      linear-gradient(rgba(126, 211, 33, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(126, 211, 33, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.chart-line {
  position: absolute;
  bottom: 20px;
  left: 10px;
  right: 10px;
  height: 80px;
}

.chart-path {
  fill: none;
  stroke: #7ed321;
  stroke-width: 2;
  filter: drop-shadow(0 0 5px rgba(126, 211, 33, 0.7));
}

.chart-area {
  fill: url(#chartGradient);
  opacity: 0.3;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  font-size: 11px;
  padding: 16px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: rgba(126, 211, 33, 0.05);
  border-radius: 4px;
  border-left: 3px solid #7ed321;
}

.stat-value {
  color: #7ed321;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 设备概览样式 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  padding: 16px;
}

.info-card {
  background: linear-gradient(135deg, rgba(126, 211, 33, 0.1), rgba(15, 25, 40, 0.8));
  border: 2px solid rgba(126, 211, 33, 0.4);
  border-radius: 10px;
  padding: 14px;
  transition: all 0.3s ease;
  min-height: 75px;
  position: relative;
  overflow: hidden;
}

.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, #7ed321, #9fff40);
  border-radius: 0 2px 2px 0;
}

.info-card:hover {
  background: linear-gradient(135deg, rgba(126, 211, 33, 0.2), rgba(15, 25, 40, 0.9));
  border-color: #7ed321;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(126, 211, 33, 0.3);
}

.info-card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding-left: 6px;
}

.info-card-header i {
  color: #7ed321;
  font-size: 14px;
  width: 16px;
  text-align: center;
}

.info-number {
  color: #7ed321;
  font-size: 12px;
  font-weight: bold;
  min-width: 20px;
  background: rgba(126, 211, 33, 0.2);
  padding: 2px 5px;
  border-radius: 3px;
  text-align: center;
}

.info-label {
  color: #ffffff;
  font-size: 13px;
  font-weight: 600;
  letter-spacing: 0.3px;
}

.info-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding-left: 6px;
}

.info-value {
  color: #7ed321;
  font-size: 15px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.info-status {
  color: rgba(255, 255, 255, 0.9);
  font-size: 11px;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.1);
  padding: 3px 6px;
  border-radius: 10px;
  display: inline-block;
  width: fit-content;
}

/* 姿态指示器样式 */
.attitude-indicator {
  width: 120px;
  height: 120px;
  margin: 20px auto;
  position: relative;
  background: radial-gradient(circle, rgba(126, 211, 33, 0.1) 30%, transparent 70%);
  border: 3px solid rgba(126, 211, 33, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.attitude-markers {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.attitude-marker {
  position: absolute;
  width: 2px;
  height: 20px;
  background: #7ed321;
  border-radius: 1px;
}

.attitude-marker:nth-child(1) {
  top: 5px;
  left: 50%;
  transform: translateX(-50%);
}

.attitude-marker:nth-child(2) {
  bottom: 5px;
  left: 50%;
  transform: translateX(-50%);
}

.attitude-marker:nth-child(3) {
  top: 50%;
  left: 5px;
  transform: translateY(-50%) rotate(90deg);
}

.attitude-marker:nth-child(4) {
  top: 50%;
  right: 5px;
  transform: translateY(-50%) rotate(90deg);
}

.attitude-inner {
  color: #7ed321;
  text-align: center;
  font-size: 12px;
  font-weight: bold;
  transition: transform 0.5s ease;
}

.attitude-data {
  padding: 16px;
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.attitude-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(126, 211, 33, 0.05);
  border: 1px solid rgba(126, 211, 33, 0.3);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.attitude-item:hover {
  background: rgba(126, 211, 33, 0.1);
  border-color: rgba(126, 211, 33, 0.5);
}

.attitude-item .label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  font-weight: 500;
}

.attitude-item .value {
  color: #7ed321;
  font-size: 14px;
  font-weight: bold;
  font-family: 'Courier New', monospace;
}

/* 任务点样式 */
.mission-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  padding: 16px;
}

.mission-point {
  background: rgba(126, 211, 33, 0.05);
  border: 1px solid rgba(126, 211, 33, 0.3);
  padding: 10px;
  border-radius: 6px;
  font-size: 11px;
}

.mission-point-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.mission-point-id {
  color: #7ed321;
  font-weight: bold;
  font-size: 10px;
}

.mission-point-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
}

.mission-point-status.completed {
  background: #7ed321;
  animation: completed-pulse 2s infinite;
}

.mission-point-status.pending {
  background: #ffd700;
  animation: pending-blink 1s infinite;
}

@keyframes completed-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

@keyframes pending-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

.mission-point-name {
  color: #ffffff;
  font-weight: bold;
  margin-bottom: 4px;
  font-size: 11px;
}

.mission-point-details {
  color: #7ed321;
  font-size: 10px;
}

/* 控制按钮样式 */
.control-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  padding: 16px;
}

.control-btn {
  background: linear-gradient(135deg, rgba(126, 211, 33, 0.1), rgba(15, 25, 40, 0.8));
  border: 2px solid rgba(126, 211, 33, 0.4);
  border-radius: 8px;
  padding: 16px 12px;
  color: #7ed321;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  font-size: 12px;
  font-weight: bold;
  min-height: 70px;
  justify-content: center;
}

.control-btn:hover {
  background: linear-gradient(135deg, rgba(126, 211, 33, 0.2), rgba(15, 25, 40, 0.9));
  border-color: #7ed321;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(126, 211, 33, 0.3);
}

.control-btn.active {
  background: linear-gradient(135deg, #7ed321, #9fff40);
  color: #000;
  border-color: #9fff40;
  box-shadow: 0 0 15px rgba(126, 211, 33, 0.6);
}

.control-btn i {
  font-size: 20px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .left-tab-panel {
    width: 300px;
  }
}

@media (max-width: 1200px) {
  .left-tab-panel {
    width: 280px;
  }

  .tab-item {
    min-width: 70px;
    font-size: 11px;
  }

  .attitude-indicator {
    width: 100px;
    height: 100px;
  }

  .info-card {
    padding: 12px;
    min-height: 70px;
  }
}

@media (max-width: 768px) {
  .left-tab-panel {
    width: 260px;
  }

  .tab-navigation {
    padding: 12px;
  }

  .tab-item {
    min-width: 60px;
    padding: 6px 8px;
    font-size: 10px;
  }

  .info-grid,
  .stats-grid,
  .mission-grid,
  .control-buttons {
    grid-template-columns: 1fr;
  }
}
</style>
