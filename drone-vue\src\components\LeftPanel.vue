<template>
  <div class="left-panel">
    <!-- 任务监控标题 -->
    <div class="panel-header">
      <i class="fas fa-tasks"></i>
      <div class="panel-title">任务监控</div>
    </div>

    <!-- 任务列表 -->
    <div class="mission-section">
      <div 
        v-for="mission in missions" 
        :key="mission.id"
        class="mission-item"
      >
        <div style="display: flex; align-items: center; gap: 8px;">
          <i :class="mission.icon"></i>
          <span>{{ mission.name }}</span>
        </div>
        <div class="mission-status">
          <i :class="mission.statusIcon"></i>
          <span>{{ mission.status }}</span>
          <div class="mission-progress">
            <div 
              class="mission-progress-fill" 
              :style="{ width: mission.progress + '%' }"
            ></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 无人机列表 -->
    <div class="drone-list">
      <div 
        v-for="drone in drones" 
        :key="drone.id"
        class="drone-item"
      >
        <div class="drone-header">
          <div class="drone-id-container">
            <div class="drone-id">{{ drone.id }}</div>
            <div class="drone-signal">
              <div 
                v-for="(bar, index) in 4" 
                :key="index"
                class="signal-bar"
                :class="{ active: index < drone.signalStrength }"
              ></div>
            </div>
          </div>
          <i class="fas fa-helicopter" :style="{ color: drone.statusColor }"></i>
        </div>
        
        <div class="drone-status-grid">
          <div 
            v-for="status in drone.statusItems" 
            :key="status.name"
            class="status-indicator"
            :class="status.type"
          >
            <i :class="status.icon"></i>
            <span>{{ status.name }}</span>
          </div>
        </div>
        
        <div class="battery-container">
          <i :class="drone.batteryIcon" class="battery-icon" :style="{ color: drone.batteryColor }"></i>
          <div class="battery-bar">
            <div 
              class="battery-fill" 
              :style="{ 
                width: drone.battery + '%',
                background: drone.batteryGradient 
              }"
            ></div>
          </div>
          <span class="battery-percentage" :style="{ color: drone.batteryColor }">{{ drone.battery }}%</span>
        </div>
      </div>
    </div>

    <!-- 数据统计 -->
    <div class="stats-section">
      <div class="panel-header">
        <i class="fas fa-chart-line"></i>
        <div class="panel-title">数据统计</div>
      </div>
      <div class="stats-chart">
        <div class="chart-grid"></div>
        <div class="chart-line">
          <svg width="100%" height="100%">
            <defs>
              <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" style="stop-color:#7ed321;stop-opacity:0.8" />
                <stop offset="100%" style="stop-color:#7ed321;stop-opacity:0.1" />
              </linearGradient>
            </defs>
            <path class="chart-area" d="M0,60 Q50,40 100,45 T200,35 T300,50 L300,80 L0,80 Z"/>
            <path class="chart-path" d="M0,60 Q50,40 100,45 T200,35 T300,50"/>
          </svg>
        </div>
      </div>
      <div class="stats-grid">
        <div 
          v-for="stat in stats" 
          :key="stat.name"
          class="stat-item"
        >
          <span>{{ stat.name }}</span>
          <div class="stat-value">
            <i :class="stat.icon" :style="{ color: stat.color }"></i>
            <span>{{ stat.value }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LeftPanel',
  data() {
    return {
      missions: [
        {
          id: 1,
          name: '任务1',
          icon: 'fas fa-play-circle',
          status: '进行中',
          statusIcon: 'fas fa-clock',
          progress: 75
        },
        {
          id: 2,
          name: '任务2',
          icon: 'fas fa-pause-circle',
          status: '待命',
          statusIcon: 'fas fa-pause',
          progress: 25
        }
      ],
      drones: [
        {
          id: 'A1',
          signalStrength: 3,
          statusColor: '#7ed321',
          battery: 95,
          batteryIcon: 'fas fa-battery-three-quarters',
          batteryColor: '#7ed321',
          batteryGradient: 'linear-gradient(90deg, #7ed321, #9fff40)',
          statusItems: [
            { name: '信号', icon: 'fas fa-signal', type: '' },
            { name: '传输', icon: 'fas fa-wifi', type: '' },
            { name: '红外', icon: 'fas fa-thermometer-half', type: '' },
            { name: '可见', icon: 'fas fa-eye', type: '' },
            { name: 'GPS', icon: 'fas fa-satellite-dish', type: '' },
            { name: '系统', icon: 'fas fa-cog', type: '' }
          ]
        },
        {
          id: 'A2',
          signalStrength: 2,
          statusColor: '#7ed321',
          battery: 85,
          batteryIcon: 'fas fa-battery-half',
          batteryColor: '#7ed321',
          batteryGradient: 'linear-gradient(90deg, #7ed321, #9fff40)',
          statusItems: [
            { name: '信号', icon: 'fas fa-signal', type: '' },
            { name: '传输', icon: 'fas fa-exclamation-triangle', type: 'warning' },
            { name: '红外', icon: 'fas fa-thermometer-half', type: '' },
            { name: '可见', icon: 'fas fa-eye', type: '' },
            { name: 'GPS', icon: 'fas fa-satellite-dish', type: '' },
            { name: '系统', icon: 'fas fa-cog', type: '' }
          ]
        },
        {
          id: 'A3',
          signalStrength: 1,
          statusColor: '#ffd700',
          battery: 75,
          batteryIcon: 'fas fa-battery-quarter',
          batteryColor: '#ffd700',
          batteryGradient: 'linear-gradient(90deg, #ffd700, #ffed4e)',
          statusItems: [
            { name: '信号', icon: 'fas fa-times-circle', type: 'error' },
            { name: '传输', icon: 'fas fa-wifi', type: 'error' },
            { name: '红外', icon: 'fas fa-thermometer-half', type: '' },
            { name: '可见', icon: 'fas fa-eye', type: '' },
            { name: 'GPS', icon: 'fas fa-satellite-dish', type: 'warning' },
            { name: '系统', icon: 'fas fa-cog', type: '' }
          ]
        }
      ],
      stats: [
        { name: '任务完成率', icon: 'fas fa-check-circle', color: '#7ed321', value: '95.0%' },
        { name: '平台利用率', icon: 'fas fa-chart-pie', color: '#7ed321', value: '98.0%' },
        { name: '处理效率', icon: 'fas fa-tachometer-alt', color: '#7ed321', value: '85.0%' },
        { name: '外观性能', icon: 'fas fa-exclamation-triangle', color: '#ffd700', value: '0' }
      ]
    }
  },
  mounted() {
    this.startDataUpdates()
  },
  methods: {
    startDataUpdates() {
      // 模拟数据更新
      setInterval(() => {
        this.updateMissionProgress()
        this.updateStats()
        this.updateSignalBars()
      }, 4000)
    },
    updateMissionProgress() {
      this.missions.forEach(mission => {
        const variation = (Math.random() - 0.5) * 5
        mission.progress = Math.max(10, Math.min(100, mission.progress + variation))
      })
    },
    updateStats() {
      this.stats.forEach(stat => {
        if (stat.value.includes('%')) {
          const baseValue = parseFloat(stat.value)
          const variation = (Math.random() - 0.5) * 3
          const newValue = Math.max(0, Math.min(100, baseValue + variation))
          stat.value = newValue.toFixed(1) + '%'
        }
      })
    },
    updateSignalBars() {
      this.drones.forEach(drone => {
        if (Math.random() > 0.7) {
          drone.signalStrength = Math.floor(Math.random() * 4) + 1
        }
      })
    }
  }
}
</script>

<style scoped>
/* ================================ 左侧面板 ================================ */
.left-panel {
    width: 280px;
    background: rgba(15, 25, 40, 0.95);
    border-right: 1px solid rgba(126, 211, 33, 0.3);
    padding: 20px;
    overflow-y: auto;
    box-shadow: inset -1px 0 10px rgba(126, 211, 33, 0.1);
}

.panel-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(126, 211, 33, 0.3);
}

.panel-header i {
    color: #7ed321;
    font-size: 18px;
}

.panel-title {
    color: #7ed321;
    font-size: 16px;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(126, 211, 33, 0.5);
}

.mission-section {
    margin-bottom: 25px;
}

.mission-item {
    background: linear-gradient(135deg, rgba(126, 211, 33, 0.15), rgba(126, 211, 33, 0.05));
    border: 1px solid rgba(126, 211, 33, 0.5);
    border-left: 4px solid #7ed321;
    padding: 12px;
    margin-bottom: 10px;
    border-radius: 6px;
    font-size: 13px;
    color: #ffffff;
    position: relative;
    transition: all 0.3s ease;
}

.mission-item:hover {
    background: linear-gradient(135deg, rgba(126, 211, 33, 0.25), rgba(126, 211, 33, 0.1));
    transform: translateX(3px);
}

.mission-item::before {
    content: '';
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background: #7ed321;
    border-radius: 50%;
    animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
    0%, 100% { opacity: 1; transform: translateY(-50%) scale(1); }
    50% { opacity: 0.5; transform: translateY(-50%) scale(1.2); }
}

.mission-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    font-size: 11px;
    color: #7ed321;
}

.mission-progress {
    flex: 1;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
}

.mission-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #7ed321, #9fff40);
    transition: width 0.5s ease;
}

/* 无人机列表 */
.drone-list {
    margin-top: 25px;
}

.drone-item {
    background: linear-gradient(135deg, rgba(126, 211, 33, 0.1), rgba(15, 25, 40, 0.8));
    border: 1px solid rgba(126, 211, 33, 0.4);
    padding: 15px;
    margin-bottom: 12px;
    border-radius: 8px;
    position: relative;
    transition: all 0.3s ease;
    overflow: hidden;
}

.drone-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #7ed321, transparent);
    animation: scan 3s linear infinite;
}

@keyframes scan {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.drone-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.drone-id-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.drone-id {
    color: #000;
    background: linear-gradient(135deg, #7ed321, #9fff40);
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 12px;
    box-shadow: 0 2px 4px rgba(126, 211, 33, 0.3);
}

.drone-signal {
    display: flex;
    gap: 2px;
}

.signal-bar {
    width: 3px;
    height: 12px;
    background: rgba(126, 211, 33, 0.3);
    border-radius: 1px;
}

.signal-bar.active {
    background: #7ed321;
    animation: signal-pulse 1s ease-in-out infinite alternate;
}

@keyframes signal-pulse {
    from { opacity: 0.7; }
    to { opacity: 1; }
}

.drone-status-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    margin-top: 8px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 10px;
    color: rgba(255, 255, 255, 0.8);
}

.status-indicator i {
    color: #7ed321;
    font-size: 12px;
}

.status-indicator.warning i {
    color: #ffd700;
}

.status-indicator.error i {
    color: #ff6b47;
}

.battery-container {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
}

.battery-icon {
    color: #7ed321;
    font-size: 14px;
}

.battery-bar {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
    border: 1px solid rgba(126, 211, 33, 0.3);
}

.battery-fill {
    height: 100%;
    background: linear-gradient(90deg, #7ed321, #9fff40);
    transition: width 0.5s ease;
    position: relative;
}

.battery-fill::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 20%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
    animation: battery-shine 2s ease-in-out infinite;
}

@keyframes battery-shine {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

.battery-percentage {
    font-size: 11px;
    color: #7ed321;
    font-weight: bold;
    min-width: 35px;
}

/* 数据统计图表 */
.stats-section {
    margin-top: 25px;
}

.stats-chart {
    background: linear-gradient(135deg, rgba(126, 211, 33, 0.1), rgba(15, 25, 40, 0.8));
    height: 140px;
    border-radius: 8px;
    border: 1px solid rgba(126, 211, 33, 0.4);
    position: relative;
    overflow: hidden;
    margin-bottom: 15px;
}

.chart-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(rgba(126, 211, 33, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(126, 211, 33, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}

.chart-line {
    position: absolute;
    bottom: 20px;
    left: 10px;
    right: 10px;
    height: 80px;
}

.chart-path {
    fill: none;
    stroke: #7ed321;
    stroke-width: 2;
    filter: drop-shadow(0 0 5px rgba(126, 211, 33, 0.7));
}

.chart-area {
    fill: url(#chartGradient);
    opacity: 0.3;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    font-size: 11px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    background: rgba(126, 211, 33, 0.05);
    border-radius: 4px;
    border-left: 3px solid #7ed321;
}

.stat-value {
    color: #7ed321;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .left-panel {
        width: 320px;
    }
}

@media (max-width: 1200px) {
    .left-panel {
        width: 280px;
    }
}
</style>
