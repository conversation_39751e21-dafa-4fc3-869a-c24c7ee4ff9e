<template>
  <div class="right-panel">
    <!-- 悬浮视频区域 -->
    <div class="floating-video-section">
      <div class="video-header">
        <div class="video-title">
          <i class="fas fa-video"></i>
          <span>单设备监控视频</span>
        </div>
        <div class="video-controls">
          <div
            v-for="control in videoControls"
            :key="control.name"
            class="video-control-btn"
            :class="{ recording: control.active && control.name === '录制' }"
            :title="control.name"
            @click="handleVideoControl(control)"
          >
            <i :class="control.icon"></i>
          </div>
        </div>
      </div>

      <div class="video-grid">
        <!-- 主视频区域 -->
        <div class="video-feed main" @click="togglePictureInPicture('main')">
          <div class="video-label">A1</div>
          <div class="video-status">
            <div class="status-light recording"></div>
            <div class="status-light"></div>
          </div>
          <div class="pip-indicator" v-if="pipVideo === 'main'">
            <i class="fas fa-expand-arrows-alt"></i>
          </div>
          <!-- 真实视频流 - 主视频 -->
          <video
            ref="mainVideo"
            autoplay
            muted
            loop
            style="width: 100%; height: 100%; object-fit: cover; background: #1a2332;"
            :poster="videoPoster"
            @loadstart="onVideoLoadStart(0)"
            @canplay="onVideoCanPlay(0)"
            @error="onVideoError(0)"
            @ended="onVideoEnded"
            @loadeddata="onVideoLoadedData"
          >
            <source :src="videoSources.main.primary" type="video/mp4">
            <source :src="videoSources.main.fallback" type="video/mp4">
            您的浏览器不支持视频播放
          </video>
        </div>

        <!-- 底部小视频区域 -->
        <div class="video-bottom-row">
          <div class="video-feed small" @click="togglePictureInPicture('secondary')">
            <div class="video-label">A2</div>
            <div class="video-status">
              <div class="status-light"></div>
            </div>
            <div class="pip-indicator" v-if="pipVideo === 'secondary'">
              <i class="fas fa-expand-arrows-alt"></i>
            </div>
            <!-- 第二个视频流 -->
            <video
              ref="secondaryVideo"
              autoplay
              muted
              loop
              style="width: 100%; height: 100%; object-fit: cover; background: #1a2332;"
              :poster="videoPoster2"
              @loadstart="onVideoLoadStart(1)"
              @canplay="onVideoCanPlay(1)"
              @error="onVideoError(1)"
              @ended="onVideoEnded"
              @loadeddata="onVideoLoadedData"
            >
              <source :src="videoSources.secondary.primary" type="video/mp4">
              <source :src="videoSources.secondary.fallback" type="video/mp4">
              您的浏览器不支持视频播放
            </video>
          </div>

          <div class="video-feed small" @click="togglePictureInPicture('third')">
            <div class="video-label">A3</div>
            <div class="video-status">
              <div class="status-light"></div>
            </div>
            <div class="pip-indicator" v-if="pipVideo === 'third'">
              <i class="fas fa-expand-arrows-alt"></i>
            </div>
            <!-- 第三个视频流 -->
            <video
              ref="thirdVideo"
              autoplay
              muted
              loop
              style="width: 100%; height: 100%; object-fit: cover; background: #1a2332;"
              :poster="videoPoster3"
              @loadstart="onVideoLoadStart(2)"
              @canplay="onVideoCanPlay(2)"
              @error="onVideoError(2)"
              @ended="onVideoEnded"
              @loadeddata="onVideoLoadedData"
            >
              <source :src="videoSources.third.primary" type="video/mp4">
              <source :src="videoSources.third.fallback" type="video/mp4">
              您的浏览器不支持视频播放
            </video>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部抽屉式选项卡 -->
    <div class="bottom-drawer-tabs">
      <div
        v-for="tab in drawerTabs"
        :key="tab.id"
        class="drawer-tab"
        :class="{ active: selectedTabs.includes(tab.id) }"
        @click="toggleTab(tab.id)"
      >
        <i :class="tab.icon"></i>
        <span>{{ tab.name }}</span>
      </div>
    </div>

    <!-- 悬浮内容面板 -->
    <div class="floating-panels">
      <!-- 设备概览面板 -->
      <div v-if="selectedTabs.includes('overview')" class="floating-panel overview-panel">
        <div class="panel-header">
          <span>设备概览</span>
          <i class="fas fa-times" @click="removeTab('overview')"></i>
        </div>
        <div class="info-grid">
          <div
            v-for="info in deviceInfo"
            :key="info.label"
            class="info-card"
          >
            <div class="info-card-header">
              <i :class="info.icon"></i>
              <span class="info-number">{{ info.number }}</span>
              <span class="info-label">{{ info.label }}</span>
            </div>
            <div class="info-details">
              <div class="info-value">{{ info.value }}</div>
              <div class="info-status">{{ info.status }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 飞行姿态面板 -->
      <div v-if="selectedTabs.includes('attitude')" class="floating-panel attitude-panel">
        <div class="panel-header">
          <span>飞行姿态</span>
          <i class="fas fa-times" @click="removeTab('attitude')"></i>
        </div>
        <div class="attitude-indicator">
          <div class="attitude-markers">
            <div
              v-for="n in 4"
              :key="n"
              class="attitude-marker"
            ></div>
          </div>
          <div class="attitude-inner" :style="{ transform: `rotate(${attitudeAngle}deg)` }">
            <div style="text-align: center;">
              <div style="font-size: 10px;">姿态角</div>
              <div style="font-size: 16px;">{{ Math.round(attitudeAngle % 360) }}°</div>
            </div>
          </div>
        </div>

        <div class="attitude-data">
          <div class="attitude-item">
            <span class="label">俯仰角</span>
            <span class="value">{{ attitudeData.pitch.toFixed(1) }}°</span>
          </div>
          <div class="attitude-item">
            <span class="label">横滚角</span>
            <span class="value">{{ attitudeData.roll.toFixed(1) }}°</span>
          </div>
          <div class="attitude-item">
            <span class="label">航向角</span>
            <span class="value">{{ attitudeData.yaw.toFixed(1) }}°</span>
          </div>
        </div>
      </div>

      <!-- 任务点面板 -->
      <div v-if="selectedTabs.includes('mission')" class="floating-panel mission-panel">
        <div class="panel-header">
          <span>任务点</span>
          <i class="fas fa-times" @click="removeTab('mission')"></i>
        </div>
        <div class="mission-grid">
          <div
            v-for="point in missionPoints"
            :key="point.id"
            class="mission-point"
          >
            <div class="mission-point-header">
              <span class="mission-point-id">{{ point.id }}</span>
              <div
                class="mission-point-status"
                :class="point.status"
              ></div>
            </div>
            <div class="mission-point-name">{{ point.name }}</div>
            <div class="mission-point-details">{{ point.details }}</div>
          </div>
        </div>
      </div>

      <!-- 设备控制面板 -->
      <div v-if="selectedTabs.includes('control')" class="floating-panel control-panel">
        <div class="panel-header">
          <span>设备控制</span>
          <i class="fas fa-times" @click="removeTab('control')"></i>
        </div>
        <div class="control-buttons">
          <div
            v-for="control in controlButtons"
            :key="control.name"
            class="control-btn"
            :class="{ active: control.active }"
            @click="handleControlButton(control)"
          >
            <i :class="control.icon"></i>
            <span>{{ control.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RightPanel',
  data() {
    return {
      // 抽屉式面板配置
      selectedTabs: ['overview'], // 当前选中显示的选项卡
      pipVideo: null, // 当前画中画的视频
      drawerTabs: [
        { id: 'overview', name: '概览', icon: 'fas fa-info-circle' },
        { id: 'attitude', name: '姿态', icon: 'fas fa-compass' },
        { id: 'mission', name: '任务', icon: 'fas fa-map-marked-alt' },
        { id: 'control', name: '控制', icon: 'fas fa-gamepad' }
      ],
      videoControls: [
        { name: '录制', icon: 'fas fa-record-vinyl', active: false },
        { name: '截图', icon: 'fas fa-camera', active: false },
        { name: '全屏', icon: 'fas fa-expand', active: false },
        { name: '设置', icon: 'fas fa-cog', active: false }
      ],
      videoSources: {
        main: {
          primary: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
          fallback: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'
        },
        secondary: {
          primary: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
          fallback: 'https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4'
        },
        third: {
          primary: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
          fallback: 'https://sample-videos.com/zip/10/mp4/SampleVideo_640x360_1mb.mp4'
        }
      },
      videoPoster: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 300'%3E%3Crect width='400' height='300' fill='%23333'/%3E%3Ctext x='200' y='150' text-anchor='middle' fill='%237ed321' font-size='16'%3E正在连接视频流...%3C/text%3E%3C/svg%3E",
      videoPoster2: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 300'%3E%3Crect width='400' height='300' fill='%23333'/%3E%3Ctext x='200' y='150' text-anchor='middle' fill='%237ed321' font-size='12'%3E红外视频流%3C/text%3E%3C/svg%3E",
      videoPoster3: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 300'%3E%3Crect width='400' height='300' fill='%23333'/%3E%3Ctext x='200' y='150' text-anchor='middle' fill='%237ed321' font-size='12'%3E可见光视频%3C/text%3E%3C/svg%3E",
      deviceInfo: [
        {
          number: '01',
          label: '建筑类型',
          icon: 'fas fa-building',
          value: '20km',
          status: '巡逻系统'
        },
        {
          number: '02',
          label: '建筑设定',
          icon: 'fas fa-cogs',
          value: '20km',
          status: '进行中'
        }
      ],
      attitudeAngle: 0,
      attitudeData: {
        pitch: 2.5,
        roll: -1.2,
        yaw: 45.8
      },
      missionPoints: [
        { id: '次序', name: '各称', details: '96.0%', status: 'completed' },
        { id: '预计到达', name: '状态', details: '97.9%', status: 'completed' },
        { id: '01', name: '建筑类型', details: '85.3%', status: 'completed' },
        { id: 'SPT5°30\'N', name: '已完成', details: '进行中', status: 'pending' }
      ],
      controlButtons: [
        { name: '定位', icon: 'fas fa-crosshairs', active: false },
        { name: '运行中', icon: 'fas fa-play', active: true },
        { name: '预设', icon: 'fas fa-cog', active: false },
        { name: '详情', icon: 'fas fa-info', active: false }
      ]
    }
  },
  mounted() {
    this.startAnimations()
  },
  methods: {
    startAnimations() {
      // 姿态仪表随机旋转
      setInterval(() => {
        const variation = (Math.random() - 0.5) * 20
        this.attitudeAngle += variation
      }, 2000)

      // 姿态数据更新
      setInterval(() => {
        this.attitudeData.pitch += (Math.random() - 0.5) * 2
        this.attitudeData.roll += (Math.random() - 0.5) * 2
        this.attitudeData.yaw += (Math.random() - 0.5) * 5

        // 限制范围
        this.attitudeData.pitch = Math.max(-90, Math.min(90, this.attitudeData.pitch))
        this.attitudeData.roll = Math.max(-180, Math.min(180, this.attitudeData.roll))
        this.attitudeData.yaw = ((this.attitudeData.yaw % 360) + 360) % 360
      }, 1500)

      // 任务点数据更新
      setInterval(() => {
        this.missionPoints.forEach(point => {
          if (point.details.includes('%')) {
            const baseValue = parseFloat(point.details)
            const variation = (Math.random() - 0.5) * 2
            const newValue = Math.max(0, Math.min(100, baseValue + variation))
            point.details = newValue.toFixed(1) + '%'
          }
        })
      }, 3000)
    },
    // 选项卡管理方法
    toggleTab(tabId) {
      const index = this.selectedTabs.indexOf(tabId)
      if (index > -1) {
        this.selectedTabs.splice(index, 1)
      } else {
        this.selectedTabs.push(tabId)
      }
    },
    removeTab(tabId) {
      const index = this.selectedTabs.indexOf(tabId)
      if (index > -1) {
        this.selectedTabs.splice(index, 1)
      }
    },
    // 画中画功能
    togglePictureInPicture(videoType) {
      if (this.pipVideo === videoType) {
        // 如果当前视频已经是画中画，则退出画中画
        this.exitPictureInPicture()
      } else {
        // 切换到新的画中画视频
        this.enterPictureInPicture(videoType)
      }
    },
    async enterPictureInPicture(videoType) {
      try {
        let videoElement
        switch(videoType) {
          case 'main':
            videoElement = this.$refs.mainVideo
            break
          case 'secondary':
            videoElement = this.$refs.secondaryVideo
            break
          case 'third':
            videoElement = this.$refs.thirdVideo
            break
        }

        if (videoElement && videoElement.requestPictureInPicture) {
          // 先退出当前的画中画
          if (document.pictureInPictureElement) {
            await document.exitPictureInPicture()
          }

          // 进入新的画中画
          await videoElement.requestPictureInPicture()
          this.pipVideo = videoType

          // 监听画中画退出事件
          videoElement.addEventListener('leavepictureinpicture', () => {
            this.pipVideo = null
          })
        }
      } catch (error) {
        console.error('画中画功能不支持或出错:', error)
      }
    },
    async exitPictureInPicture() {
      try {
        if (document.pictureInPictureElement) {
          await document.exitPictureInPicture()
        }
        this.pipVideo = null
      } catch (error) {
        console.error('退出画中画出错:', error)
      }
    },
    handleVideoControl(control) {
      // 清除其他控制状态
      this.videoControls.forEach(ctrl => {
        if (ctrl !== control) ctrl.active = false
      })

      // 切换当前控制状态
      control.active = !control.active

      switch(control.name) {
        case '录制':
          this.handleRecording(control.active)
          break
        case '截图':
          this.takeScreenshot()
          control.active = false // 截图是瞬时操作
          break
        case '全屏':
          this.toggleFullscreen(control.active)
          break
        case '设置':
          this.showVideoSettings(control.active)
          break
      }
    },
    handleRecording(isRecording) {
      if (isRecording) {
        console.log('开始录制视频')
        // 这里可以添加实际的录制逻辑
      } else {
        console.log('停止录制视频')
      }
    },
    takeScreenshot() {
      console.log('截图已保存')
      // 这里可以添加实际的截图逻辑
    },
    toggleFullscreen(isFullscreen) {
      if (isFullscreen) {
        if (this.$refs.mainVideo && this.$refs.mainVideo.requestFullscreen) {
          this.$refs.mainVideo.requestFullscreen()
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        }
      }
    },
    showVideoSettings(show) {
      if (show) {
        console.log('显示视频设置')
      } else {
        console.log('隐藏视频设置')
      }
    },
    handleControlButton(control) {
      // 清除其他按钮状态（除了运行中）
      if (control.name !== '运行中') {
        this.controlButtons.forEach(btn => {
          if (btn !== control && btn.name !== '运行中') {
            btn.active = false
          }
        })
      }

      control.active = !control.active

      switch(control.name) {
        case '定位':
          this.handlePositioning(control.active)
          break
        case '运行中':
          this.handleRunning(control.active)
          break
        case '预设':
          this.handlePreset(control.active)
          break
        case '详情':
          this.handleDetails(control.active)
          break
      }
    },
    handlePositioning(active) {
      console.log(active ? '开始定位' : '停止定位')
    },
    handleRunning(active) {
      console.log(active ? '系统运行中' : '系统已停止')
    },
    handlePreset(active) {
      console.log(active ? '显示预设' : '隐藏预设')
    },
    handleDetails(active) {
      console.log(active ? '显示详情' : '隐藏详情')
    },
    // 视频事件处理
    onVideoLoadStart(index) {
      console.log(`视频 ${index + 1} 开始加载`)
    },
    onVideoCanPlay(index) {
      console.log(`视频 ${index + 1} 可以播放`)
    },
    onVideoError(index) {
      console.log(`视频 ${index + 1} 加载失败，尝试备用源`)
    },
    onVideoEnded() {
      console.log('视频播放结束')
    },
    onVideoLoadedData() {
      console.log('视频数据加载完成')
    }
  }
}
</script>

<style scoped>
/* ================================ 右侧面板 ================================ */
.right-panel {
    position: fixed;
    top: 0;
    right: 0;
    width: 100%;
    height: 100vh;
    pointer-events: none;
    z-index: 1000;
    display: flex;
    flex-direction: column;
}

/* 悬浮视频区域 */
.floating-video-section {
    position: absolute;
    top: 80px;
    right: 20px;
    width: 280px;
    background: linear-gradient(180deg, rgba(26, 35, 50, 0.95) 0%, rgba(15, 20, 25, 0.95) 100%);
    border: 1px solid rgba(126, 211, 33, 0.3);
    border-radius: 8px;
    backdrop-filter: blur(10px);
    pointer-events: auto;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    padding: 16px;
}

.video-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.video-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #7ed321;
    font-size: 14px;
    font-weight: bold;
}

.video-controls {
    display: flex;
    gap: 8px;
}

.video-control-btn {
    width: 32px;
    height: 32px;
    background: rgba(126, 211, 33, 0.1);
    border: 1px solid rgba(126, 211, 33, 0.4);
    border-radius: 6px;
    color: #7ed321;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 14px;
}

.video-control-btn:hover {
    background: rgba(126, 211, 33, 0.2);
    border-color: #7ed321;
    transform: translateY(-1px);
}

.video-control-btn.recording {
    background: linear-gradient(135deg, #ff6b47, #ff8a73);
    border-color: #ff6b47;
    color: #fff;
    animation: recording-pulse 1s infinite;
}

@keyframes recording-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.video-grid {
    display: flex;
    flex-direction: column;
    gap: 10px;
    height: 400px;
}

.video-feed {
    position: relative;
    background: #1a2332;
    border: 2px solid rgba(126, 211, 33, 0.4);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.video-feed:hover {
    border-color: #7ed321;
    box-shadow: 0 0 15px rgba(126, 211, 33, 0.3);
}

/* 画中画指示器 */
.pip-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    background: rgba(126, 211, 33, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000;
    font-size: 10px;
    z-index: 15;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.6; }
    100% { opacity: 1; }
}

.video-feed.main {
    flex: 1;
    border-color: #7ed321;
    box-shadow: 0 0 20px rgba(126, 211, 33, 0.4);
}

/* 底部小视频行 */
.video-bottom-row {
    display: flex;
    gap: 10px;
    height: 120px;
}

.video-feed.small {
    flex: 1;
    height: 100%;
}

.video-label {
    position: absolute;
    top: 8px;
    left: 8px;
    background: rgba(0, 0, 0, 0.8);
    color: #7ed321;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    z-index: 10;
}

.video-status {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    gap: 4px;
    z-index: 10;
}

.status-light {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
}

.status-light.recording {
    background: #ff6b47;
    animation: status-blink 1s infinite;
}

@keyframes status-blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* 右侧竖直选项卡 */
.bottom-drawer-tabs {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 8px;
    pointer-events: auto;
    z-index: 1001;
}

.drawer-tab {
    padding: 16px 12px;
    background: rgba(26, 35, 50, 0.9);
    border: 1px solid rgba(126, 211, 33, 0.3);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: rgba(255, 255, 255, 0.6);
    font-size: 11px;
    font-weight: 500;
    backdrop-filter: blur(10px);
    width: 60px;
    height: 60px;
    justify-content: center;
}

.drawer-tab:hover {
    background: rgba(126, 211, 33, 0.1);
    color: rgba(255, 255, 255, 0.8);
    border-color: rgba(126, 211, 33, 0.5);
    transform: translateY(-2px);
}

.drawer-tab.active {
    background: linear-gradient(135deg, rgba(126, 211, 33, 0.2), rgba(126, 211, 33, 0.1));
    color: #7ed321;
    border-color: #7ed321;
    box-shadow: 0 4px 15px rgba(126, 211, 33, 0.3);
}

.drawer-tab i {
    font-size: 18px;
    margin-bottom: 2px;
}

/* 悬浮内容面板 */
.floating-panels {
    position: fixed;
    top: 50%;
    right: 100px;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 16px;
    pointer-events: auto;
    z-index: 1000;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
}

.floating-panel {
    width: 280px;
    background: linear-gradient(180deg, rgba(26, 35, 50, 0.95) 0%, rgba(15, 20, 25, 0.95) 100%);
    border: 1px solid rgba(126, 211, 33, 0.3);
    border-radius: 8px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    animation: slideInRight 0.3s ease;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid rgba(126, 211, 33, 0.2);
    color: #7ed321;
    font-size: 14px;
    font-weight: bold;
}

.panel-header i {
    cursor: pointer;
    color: rgba(255, 255, 255, 0.6);
    transition: color 0.3s ease;
}

.panel-header i:hover {
    color: #ff6b6b;
}

.floating-panel .info-grid,
.floating-panel .attitude-indicator,
.floating-panel .attitude-data,
.floating-panel .mission-grid,
.floating-panel .control-buttons {
    padding: 16px;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .floating-video-section,
    .floating-panel {
        width: 260px;
    }

    .bottom-drawer-tabs {
        right: 10px;
    }

    .floating-panels {
        right: 80px;
    }
}

@media (max-width: 480px) {
    .floating-video-section,
    .floating-panel {
        width: calc(100vw - 120px);
    }

    .drawer-tab {
        width: 50px;
        height: 50px;
        padding: 12px 8px;
    }

    .drawer-tab i {
        font-size: 16px;
    }

    .floating-panels {
        right: 70px;
    }
}

.section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(126, 211, 33, 0.3);
}

.section-header i {
    color: #7ed321;
    font-size: 16px;
}

.section-title {
    color: #7ed321;
    font-size: 14px;
    font-weight: bold;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 20px;
}

.info-card {
    background: linear-gradient(135deg, rgba(126, 211, 33, 0.1), rgba(15, 25, 40, 0.8));
    border: 2px solid rgba(126, 211, 33, 0.4);
    border-radius: 10px;
    padding: 14px;
    transition: all 0.3s ease;
    min-height: 75px;
    position: relative;
    overflow: hidden;
}

.info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #7ed321, #9fff40);
    border-radius: 0 2px 2px 0;
}

.info-card:hover {
    background: linear-gradient(135deg, rgba(126, 211, 33, 0.2), rgba(15, 25, 40, 0.9));
    border-color: #7ed321;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(126, 211, 33, 0.3);
}

.info-card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    padding-left: 6px;
}

.info-card-header i {
    color: #7ed321;
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.info-number {
    color: #7ed321;
    font-size: 12px;
    font-weight: bold;
    min-width: 20px;
    background: rgba(126, 211, 33, 0.2);
    padding: 2px 5px;
    border-radius: 3px;
    text-align: center;
}

.info-label {
    color: #ffffff;
    font-size: 13px;
    font-weight: 600;
    letter-spacing: 0.3px;
}

.info-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding-left: 6px;
}

.info-value {
    color: #7ed321;
    font-size: 15px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.info-status {
    color: rgba(255, 255, 255, 0.9);
    font-size: 11px;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.1);
    padding: 3px 6px;
    border-radius: 10px;
    display: inline-block;
    width: fit-content;
}

/* 姿态指示器 */
.attitude-indicator {
    width: 120px;
    height: 120px;
    margin: 20px auto;
    position: relative;
    background: radial-gradient(circle, rgba(126, 211, 33, 0.1) 30%, transparent 70%);
    border: 3px solid rgba(126, 211, 33, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.attitude-markers {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.attitude-marker {
    position: absolute;
    width: 2px;
    height: 20px;
    background: #7ed321;
    border-radius: 1px;
}

.attitude-marker:nth-child(1) {
    top: 5px;
    left: 50%;
    transform: translateX(-50%);
}

.attitude-marker:nth-child(2) {
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
}

.attitude-marker:nth-child(3) {
    top: 50%;
    left: 5px;
    transform: translateY(-50%) rotate(90deg);
}

.attitude-marker:nth-child(4) {
    top: 50%;
    right: 5px;
    transform: translateY(-50%) rotate(90deg);
}

.attitude-inner {
    color: #7ed321;
    text-align: center;
    font-size: 12px;
    font-weight: bold;
    transition: transform 0.5s ease;
}

/* 姿态数据 */
.attitude-data {
    margin-top: 20px;
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
}

.attitude-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: rgba(126, 211, 33, 0.05);
    border: 1px solid rgba(126, 211, 33, 0.3);
    border-radius: 6px;
    transition: all 0.3s ease;
}

.attitude-item:hover {
    background: rgba(126, 211, 33, 0.1);
    border-color: rgba(126, 211, 33, 0.5);
}

.attitude-item .label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    font-weight: 500;
}

.attitude-item .value {
    color: #7ed321;
    font-size: 14px;
    font-weight: bold;
    font-family: 'Courier New', monospace;
}

/* 任务点网格 */
.mission-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 20px;
}

.mission-point {
    background: rgba(126, 211, 33, 0.05);
    border: 1px solid rgba(126, 211, 33, 0.3);
    padding: 10px;
    border-radius: 6px;
    font-size: 11px;
}

.mission-point-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.mission-point-id {
    color: #7ed321;
    font-weight: bold;
    font-size: 10px;
}

.mission-point-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
}

.mission-point-status.completed {
    background: #7ed321;
    animation: completed-pulse 2s infinite;
}

.mission-point-status.pending {
    background: #ffd700;
    animation: pending-blink 1s infinite;
}

@keyframes completed-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

@keyframes pending-blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.mission-point-name {
    color: #ffffff;
    font-weight: bold;
    margin-bottom: 4px;
    font-size: 11px;
}

.mission-point-details {
    color: #7ed321;
    font-size: 10px;
}

/* 控制按钮组 */
.control-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-top: 20px;
}

.control-btn {
    background: linear-gradient(135deg, rgba(126, 211, 33, 0.1), rgba(15, 25, 40, 0.8));
    border: 2px solid rgba(126, 211, 33, 0.4);
    border-radius: 8px;
    padding: 16px 12px;
    color: #7ed321;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    font-size: 12px;
    font-weight: bold;
    min-height: 70px;
    justify-content: center;
}

.control-btn:hover {
    background: linear-gradient(135deg, rgba(126, 211, 33, 0.2), rgba(15, 25, 40, 0.9));
    border-color: #7ed321;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(126, 211, 33, 0.3);
}

.control-btn.active {
    background: linear-gradient(135deg, #7ed321, #9fff40);
    color: #000;
    border-color: #9fff40;
    box-shadow: 0 0 15px rgba(126, 211, 33, 0.6);
}

.control-btn i {
    font-size: 20px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .right-panel {
        width: 360px;
    }

    .video-grid {
        height: 350px;
    }
}

@media (max-width: 1200px) {
    .right-panel {
        width: 340px;
    }

    .video-grid {
        height: 320px;
    }

    .attitude-indicator {
        width: 100px;
        height: 100px;
    }

    .info-card {
        padding: 14px;
        min-height: 80px;
    }

    .info-value {
        font-size: 16px;
    }

    .info-label {
        font-size: 14px;
    }
}
</style>
