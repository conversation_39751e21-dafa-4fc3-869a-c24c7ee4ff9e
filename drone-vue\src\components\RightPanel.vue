<template>
  <div class="right-panel">
    <!-- 悬浮视频区域 -->
    <div class="floating-video-section">
      <div class="video-header">
        <div class="video-title">
          <i class="fas fa-video"></i>
          <span>单设备监控视频</span>
        </div>
        <div class="video-controls">
          <div
            v-for="control in videoControls"
            :key="control.name"
            class="video-control-btn"
            :class="{ recording: control.active && control.name === '录制' }"
            :title="control.name"
            @click="handleVideoControl(control)"
          >
            <i :class="control.icon"></i>
          </div>
        </div>
      </div>

      <div class="video-grid">
        <!-- 主视频区域 -->
        <div class="video-feed main" @click="togglePictureInPicture('main')">
          <div class="video-label">A1</div>
          <div class="video-status">
            <div class="status-light recording"></div>
            <div class="status-light"></div>
          </div>
          <div class="pip-indicator" v-if="pipVideo === 'main'">
            <i class="fas fa-expand-arrows-alt"></i>
          </div>
          <!-- 真实视频流 - 主视频 -->
          <video
            ref="mainVideo"
            autoplay
            muted
            loop
            style="width: 100%; height: 100%; object-fit: cover; background: #1a2332;"
            :poster="videoPoster"
            @loadstart="onVideoLoadStart(0)"
            @canplay="onVideoCanPlay(0)"
            @error="onVideoError(0)"
            @ended="onVideoEnded"
            @loadeddata="onVideoLoadedData"
          >
            <source :src="videoSources.main.primary" type="video/mp4">
            <source :src="videoSources.main.fallback" type="video/mp4">
            您的浏览器不支持视频播放。
          </video>
        </div>

        <!-- 底部小视频区域 -->
        <div class="video-bottom-row">
          <div class="video-feed small" @click="togglePictureInPicture('secondary')">
            <div class="video-label">A2</div>
            <div class="video-status">
              <div class="status-light"></div>
            </div>
            <div class="pip-indicator" v-if="pipVideo === 'secondary'">
              <i class="fas fa-expand-arrows-alt"></i>
            </div>
            <!-- 第二个视频流 -->
            <video
              ref="secondaryVideo"
              autoplay
              muted
              loop
              style="width: 100%; height: 100%; object-fit: cover; background: #1a2332;"
              :poster="videoPoster2"
              @loadstart="onVideoLoadStart(1)"
              @canplay="onVideoCanPlay(1)"
              @error="onVideoError(1)"
              @ended="onVideoEnded"
              @loadeddata="onVideoLoadedData"
            >
              <source :src="videoSources.secondary.primary" type="video/mp4">
              <source :src="videoSources.secondary.fallback" type="video/mp4">
              您的浏览器不支持视频播放。
            </video>
          </div>

          <div class="video-feed small" @click="togglePictureInPicture('third')">
            <div class="video-label">A3</div>
            <div class="video-status">
              <div class="status-light"></div>
            </div>
            <div class="pip-indicator" v-if="pipVideo === 'third'">
              <i class="fas fa-expand-arrows-alt"></i>
            </div>
            <!-- 第三个视频流 -->
            <video
              ref="thirdVideo"
              autoplay
              muted
              loop
              style="width: 100%; height: 100%; object-fit: cover; background: #1a2332;"
              :poster="videoPoster3"
              @loadstart="onVideoLoadStart(2)"
              @canplay="onVideoCanPlay(2)"
              @error="onVideoError(2)"
              @ended="onVideoEnded"
              @loadeddata="onVideoLoadedData"
            >
              <source :src="videoSources.third.primary" type="video/mp4">
              <source :src="videoSources.third.fallback" type="video/mp4">
              您的浏览器不支持视频播放。
            </video>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RightPanel',
  data() {
    return {
      pipVideo: null, // 当前画中画的视频
      videoControls: [
        { name: '录制', icon: 'fas fa-record-vinyl', active: false },
        { name: '截图', icon: 'fas fa-camera', active: false },
        { name: '全屏', icon: 'fas fa-expand', active: false },
        { name: '设置', icon: 'fas fa-cog', active: false }
      ],
      videoSources: {
        main: {
          primary: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
          fallback: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'
        },
        secondary: {
          primary: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
          fallback: 'https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4'
        },
        third: {
          primary: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
          fallback: 'https://sample-videos.com/zip/10/mp4/SampleVideo_640x360_1mb.mp4'
        }
      },
      videoPoster: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 300'%3E%3Crect width='400' height='300' fill='%23333'/%3E%3Ctext x='200' y='150' text-anchor='middle' fill='%237ed321' font-size='16'%3E正在连接视频流...%3C/text%3E%3C/svg%3E",
      videoPoster2: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 300'%3E%3Crect width='400' height='300' fill='%23333'/%3E%3Ctext x='200' y='150' text-anchor='middle' fill='%237ed321' font-size='12'%3E红外视频流%3C/text%3E%3C/svg%3E",
      videoPoster3: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 300'%3E%3Crect width='400' height='300' fill='%23333'/%3E%3Ctext x='200' y='150' text-anchor='middle' fill='%237ed321' font-size='12'%3E可见光视频%3C/text%3E%3C/svg%3E"
    }
  },
  methods: {
    // 画中画功能
    togglePictureInPicture(videoType) {
      if (this.pipVideo === videoType) {
        // 如果当前视频已经是画中画，则退出画中画
        this.exitPictureInPicture()
      } else {
        // 切换到新的画中画视频
        this.enterPictureInPicture(videoType)
      }
    },
    async enterPictureInPicture(videoType) {
      try {
        let videoElement
        switch(videoType) {
          case 'main':
            videoElement = this.$refs.mainVideo
            break
          case 'secondary':
            videoElement = this.$refs.secondaryVideo
            break
          case 'third':
            videoElement = this.$refs.thirdVideo
            break
        }

        if (videoElement && videoElement.requestPictureInPicture) {
          // 先退出当前的画中画
          if (document.pictureInPictureElement) {
            await document.exitPictureInPicture()
          }

          await videoElement.requestPictureInPicture()
          this.pipVideo = videoType
        }
      } catch (error) {
        console.error('进入画中画出错:', error)
      }
    },
    async exitPictureInPicture() {
      try {
        if (document.pictureInPictureElement) {
          await document.exitPictureInPicture()
        }
        this.pipVideo = null
      } catch (error) {
        console.error('退出画中画出错:', error)
      }
    },
    handleVideoControl(control) {
      // 清除其他控制状态
      this.videoControls.forEach(ctrl => {
        if (ctrl !== control) ctrl.active = false
      })

      // 切换当前控制状态
      control.active = !control.active

      switch(control.name) {
        case '录制':
          this.handleRecording(control.active)
          break
        case '截图':
          this.takeScreenshot()
          control.active = false // 截图是瞬时操作
          break
        case '全屏':
          this.toggleFullscreen(control.active)
          break
        case '设置':
          this.showVideoSettings(control.active)
          break
      }
    },
    handleRecording(isRecording) {
      if (isRecording) {
        console.log('开始录制视频')
        // 这里可以添加实际的录制逻辑
      } else {
        console.log('停止录制视频')
      }
    },
    takeScreenshot() {
      console.log('截图已保存')
      // 这里可以添加实际的截图逻辑
    },
    toggleFullscreen(isFullscreen) {
      if (isFullscreen) {
        if (this.$refs.mainVideo && this.$refs.mainVideo.requestFullscreen) {
          this.$refs.mainVideo.requestFullscreen()
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        }
      }
    },
    showVideoSettings(show) {
      if (show) {
        console.log('显示视频设置')
      } else {
        console.log('隐藏视频设置')
      }
    },

    // 视频事件处理
    onVideoLoadStart(index) {
      console.log(`视频 ${index + 1} 开始加载`)
    },
    onVideoCanPlay(index) {
      console.log(`视频 ${index + 1} 可以播放`)
    },
    onVideoError(index) {
      console.log(`视频 ${index + 1} 加载失败，尝试备用源`)
    },
    onVideoEnded() {
      console.log('视频播放结束')
    },
    onVideoLoadedData() {
      console.log('视频数据加载完成')
    }
  }
}
</script>

<style scoped>
/* ================================ 右侧面板 ================================ */
.right-panel {
    position: fixed;
    top: 0;
    right: 0;
    width: 100%;
    height: 100vh;
    pointer-events: none;
    z-index: 1000;
    display: flex;
    flex-direction: column;
}

/* 悬浮视频区域 */
.floating-video-section {
    position: absolute;
    top: 80px;
    right: 20px;
    width: 280px;
    background: linear-gradient(180deg, rgba(26, 35, 50, 0.95) 0%, rgba(15, 20, 25, 0.95) 100%);
    border: 1px solid rgba(126, 211, 33, 0.3);
    border-radius: 8px;
    backdrop-filter: blur(10px);
    pointer-events: auto;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    padding: 16px;
}

.video-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.video-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #7ed321;
    font-size: 14px;
    font-weight: bold;
}

.video-title i {
    font-size: 16px;
}

.video-controls {
    display: flex;
    gap: 8px;
}

.video-control-btn {
    width: 32px;
    height: 32px;
    background: rgba(126, 211, 33, 0.1);
    border: 1px solid rgba(126, 211, 33, 0.3);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #7ed321;
}

.video-control-btn:hover {
    background: rgba(126, 211, 33, 0.2);
    border-color: #7ed321;
}

.video-control-btn.recording {
    background: rgba(255, 107, 71, 0.2);
    border-color: #ff6b47;
    color: #ff6b47;
    animation: recording-pulse 1s infinite;
}

@keyframes recording-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.video-grid {
    display: flex;
    flex-direction: column;
    gap: 10px;
    height: 400px;
}

.video-feed {
    position: relative;
    background: #1a2332;
    border: 2px solid rgba(126, 211, 33, 0.4);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.video-feed:hover {
    border-color: #7ed321;
    box-shadow: 0 0 15px rgba(126, 211, 33, 0.3);
}

/* 画中画指示器 */
.pip-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    background: rgba(126, 211, 33, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000;
    font-size: 10px;
    z-index: 15;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.6; }
    100% { opacity: 1; }
}

.video-feed.main {
    flex: 1;
    border-color: #7ed321;
    box-shadow: 0 0 20px rgba(126, 211, 33, 0.4);
}

/* 底部小视频行 */
.video-bottom-row {
    display: flex;
    gap: 10px;
    height: 120px;
}

.video-feed.small {
    flex: 1;
    height: 100%;
}

.video-label {
    position: absolute;
    top: 8px;
    left: 8px;
    background: rgba(0, 0, 0, 0.8);
    color: #7ed321;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    z-index: 10;
}

.video-status {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    gap: 4px;
    z-index: 10;
}

.status-light {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
}

.status-light.recording {
    background: #ff6b47;
    animation: status-blink 1s infinite;
}

@keyframes status-blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .floating-video-section {
        width: 260px;
    }
}

@media (max-width: 480px) {
    .floating-video-section {
        width: calc(100vw - 40px);
        right: 10px;
    }
}
</style>