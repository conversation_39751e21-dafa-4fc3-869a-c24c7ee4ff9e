<template>
  <div class="map-container">
    <!-- 真实地图显示 -->
    <div id="realMap" ref="mapContainer"></div>
    
    <div class="map-overlay"></div>
    
    <!-- 地图头部信息 -->
    <div class="map-header">
      <div class="header-item">
        <i class="fas fa-user-cog"></i>
        <span>操作员A</span>
      </div>
      <div class="header-item">
        <i class="fas fa-clock"></i>
        <span class="time-info">{{ currentTime }}</span>
      </div>
      <div class="header-item">
        <i class="fas fa-map"></i>
        <span>{{ currentMapType }}</span>
      </div>
      <div class="header-item">
        <div class="status-dot"></div>
        <span>系统状态 • 正常</span>
      </div>
      <div class="header-item">
        <i class="fas fa-satellite"></i>
        <span>RTK差分</span>
      </div>
      <div class="header-item">
        <i class="fas fa-wifi"></i>
        <span>带宽: {{ bandwidth }}/100 Mbps</span>
      </div>
    </div>

    <!-- 无人机标记 (HTML覆盖层) -->
    <div 
      class="drone-marker" 
      :style="{ top: dronePosition.top, left: dronePosition.left }"
    >
      <div class="drone-radar"></div>
      <div class="drone-trail"></div>
      <div class="drone-icon-container">
        <div class="drone-direction">
          <i class="fas fa-location-arrow"></i>
        </div>
        <i class="fas fa-helicopter"></i>
      </div>
    </div>

    <!-- 任务区域标记 -->
    <div 
      v-for="mission in missionMarkers" 
      :key="mission.id"
      class="mission-marker" 
      :style="{ top: mission.top, left: mission.left }"
    >
      <div class="mission-box">
        <div class="mission-header">
          <i :class="mission.icon"></i>
          <span>{{ mission.title }}</span>
        </div>
        <div class="mission-info">
          <i :class="mission.infoIcon"></i> {{ mission.info }}<br>
          <i class="fas fa-map-marker-alt"></i> {{ mission.location }}
        </div>
      </div>
    </div>

    <!-- 无人机操控按钮组 -->
    <div class="drone-controls">
      <div 
        v-for="control in droneControls" 
        :key="control.name"
        class="drone-control-btn"
        :class="{ 
          active: control.active, 
          emergency: control.emergency 
        }"
        :title="control.name"
        @click="handleDroneControl(control)"
      >
        <i :class="control.icon"></i>
        <span class="btn-label">{{ control.name }}</span>
      </div>
    </div>

    <!-- 地图控制按钮 -->
    <div class="map-controls">
      <div class="map-control-btn" title="放大" @click="zoomIn">
        <i class="fas fa-plus"></i>
      </div>
      <div class="map-control-btn" title="缩小" @click="zoomOut">
        <i class="fas fa-minus"></i>
      </div>
      <div class="map-control-btn" title="定位" @click="centerMap">
        <i class="fas fa-crosshairs"></i>
      </div>
      <div class="map-control-btn" title="图层" @click="toggleLayer">
        <i class="fas fa-layer-group"></i>
      </div>
    </div>

    <!-- 地形图 -->
    <div class="terrain-map">
      <div class="terrain-header">
        <i class="fas fa-mountain"></i>
        <span>地形图 10km</span>
      </div>
      <svg width="100%" height="90px" style="padding: 0 15px;">
        <defs>
          <linearGradient id="terrainGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style="stop-color:#7ed321;stop-opacity:0.8" />
            <stop offset="100%" style="stop-color:#7ed321;stop-opacity:0.2" />
          </linearGradient>
        </defs>
        <path d="M10,70 Q40,30 70,40 T130,25 T190,45 T210,30" 
              stroke="#7ed321" 
              stroke-width="2" 
              fill="none"
              filter="drop-shadow(0 0 3px rgba(126, 211, 33, 0.5))"/>
        <path d="M10,70 Q40,30 70,40 T130,25 T190,45 T210,30 L210,70 L10,70 Z" 
              fill="url(#terrainGradient)"/>
      </svg>
    </div>

    <!-- 状态提示 -->
    <div v-if="statusMessage" class="status-toast">
      {{ statusMessage }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'MapContainer',
  data() {
    return {
      map: null,
      droneMarker: null,
      missionMarkers: [
        {
          id: 1,
          top: '35%',
          left: '60%',
          icon: 'fas fa-crosshairs',
          title: '任务区域',
          infoIcon: 'fas fa-search',
          info: '排查点-12:00:00 A.M',
          location: '收集点 2km'
        },
        {
          id: 2,
          top: '65%',
          left: '75%',
          icon: 'fas fa-download',
          title: '收集区域',
          infoIcon: 'fas fa-database',
          info: '收集区-12:00:00 A.M',
          location: '收集点 1.5km'
        }
      ],
      dronePosition: {
        top: '45%',
        left: '45%'
      },
      currentTime: '',
      currentMapType: 'OpenStreetMap',
      bandwidth: 58,
      currentMapLayer: 'osm',
      mapLayers: {},
      statusMessage: '',
      droneControls: [
        { name: '起飞', icon: 'fas fa-rocket', active: false, emergency: false },
        { name: '降落', icon: 'fas fa-landmark', active: false, emergency: false },
        { name: '悬停', icon: 'fas fa-pause-circle', active: false, emergency: false },
        { name: '返航', icon: 'fas fa-undo-alt', active: false, emergency: false },
        { name: '停止', icon: 'fas fa-hand-paper', active: false, emergency: true },
        { name: '锁定', icon: 'fas fa-lock', active: false, emergency: false }
      ],
      currentMode: null
    }
  },
  mounted() {
    this.initializeMap()
    this.updateTime()
    this.startAnimations()
    setInterval(this.updateTime, 1000)
  },
  methods: {
    initializeMap() {
      // 创建地图，设置中心点为北京
      this.map = L.map(this.$refs.mapContainer, {
        center: [39.9042, 116.4074], // 北京坐标
        zoom: 13,
        zoomControl: false // 禁用默认缩放控件
      })

      // 添加OpenStreetMap图层
      const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/">OpenStreetMap</a>',
        maxZoom: 19
      })

      // 添加卫星图层备选
      const satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
        attribution: '&copy; <a href="https://www.esri.com/">Esri</a>',
        maxZoom: 19
      })

      // 默认使用OSM图层
      osmLayer.addTo(this.map)

      // 存储图层引用
      this.mapLayers = {
        osm: osmLayer,
        satellite: satelliteLayer
      }

      // 添加无人机标记
      this.addDroneMarker()
      
      // 添加任务区域标记
      this.addMissionMarkers()

      // 无人机实时移动
      this.startDroneMovement()
    },
    addDroneMarker() {
      // 创建自定义无人机图标
      const droneIcon = L.divIcon({
        className: 'custom-drone-marker',
        html: '<i class="fas fa-helicopter"></i>',
        iconSize: [60, 60],
        iconAnchor: [30, 30]
      })

      // 在地图上添加无人机标记
      this.droneMarker = L.marker([39.9042, 116.4074], {icon: droneIcon})
        .addTo(this.map)
        .bindPopup(`
          <div style="color: #7ed321; font-weight: bold;">
            <i class="fas fa-helicopter"></i> 无人机 A1<br>
            <small>状态: 悬停中</small><br>
            <small>高度: 120m</small><br>
            <small>电量: 95%</small>
          </div>
        `)
    },
    addMissionMarkers() {
      // 任务区域1
      const mission1 = L.marker([39.9100, 116.4200], {
        icon: L.divIcon({
          className: 'custom-mission-marker',
          html: `
            <div>
              <i class="fas fa-crosshairs"></i> 任务区域<br>
              <small>排查点-12:00:00 A.M</small><br>
              <small>收集点 2km</small>
            </div>
          `,
          iconSize: [140, 80],
          iconAnchor: [70, 40]
        })
      }).addTo(this.map)

      // 任务区域2
      const mission2 = L.marker([39.8980, 116.4300], {
        icon: L.divIcon({
          className: 'custom-mission-marker',
          html: `
            <div>
              <i class="fas fa-download"></i> 收集区域<br>
              <small>收集区-12:00:00 A.M</small><br>
              <small>收集点 1.5km</small>
            </div>
          `,
          iconSize: [140, 80],
          iconAnchor: [70, 40]
        })
      }).addTo(this.map)
    },
    startDroneMovement() {
      let moveIndex = 0
      const movePoints = [
        [39.9042, 116.4074],
        [39.9060, 116.4090],
        [39.9080, 116.4110],
        [39.9070, 116.4130],
        [39.9050, 116.4120],
        [39.9042, 116.4074]
      ]

      setInterval(() => {
        if (this.droneMarker) {
          moveIndex = (moveIndex + 1) % movePoints.length
          this.droneMarker.setLatLng(movePoints[moveIndex])
        }
      }, 5000)
    },
    startAnimations() {
      // 模拟无人机移动（HTML标记动画）
      this.animateDrone()
      // 更新带宽数据
      setInterval(() => {
        this.bandwidth = Math.floor(Math.random() * 40) + 50
      }, 3000)
    },
    animateDrone() {
      let x = 45
      let y = 45
      let dx = 0.3
      let dy = 0.2

      setInterval(() => {
        x += dx
        y += dy

        if (x > 80 || x < 20) dx = -dx
        if (y > 70 || y < 30) dy = -dy

        this.dronePosition = {
          top: y + '%',
          left: x + '%'
        }
      }, 3000)
    },
    updateTime() {
      const now = new Date()
      const timeStr = now.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
      this.currentTime = `CST ${timeStr.replace(',', '')}`
    },
    // 地图控制方法
    zoomIn() {
      if (this.map) {
        this.map.zoomIn()
      }
    },
    zoomOut() {
      if (this.map) {
        this.map.zoomOut()
      }
    },
    centerMap() {
      if (this.map && this.droneMarker) {
        this.map.setView(this.droneMarker.getLatLng(), 15)
      }
    },
    toggleLayer() {
      if (this.map && this.mapLayers) {
        if (this.currentMapLayer === 'osm') {
          this.map.removeLayer(this.mapLayers.osm)
          this.map.addLayer(this.mapLayers.satellite)
          this.currentMapLayer = 'satellite'
          this.currentMapType = '卫星地图'
        } else {
          this.map.removeLayer(this.mapLayers.satellite)
          this.map.addLayer(this.mapLayers.osm)
          this.currentMapLayer = 'osm'
          this.currentMapType = 'OpenStreetMap'
        }
      }
    },
    // 无人机控制方法
    handleDroneControl(control) {
      // 清除之前的活动状态
      this.droneControls.forEach(ctrl => {
        ctrl.active = false
        ctrl.emergency = ctrl.name === '停止'
      })

      // 设置当前控制为活动状态
      control.active = true

      // 处理不同的操控命令
      switch(control.name) {
        case '起飞':
          this.handleTakeoff()
          break
        case '降落':
          this.handleLanding()
          break
        case '悬停':
          this.handleHover()
          break
        case '返航':
          this.handleReturnHome()
          break
        case '停止':
          this.handleEmergencyStop()
          break
        case '锁定':
          this.handleLock(control)
          break
      }
    },
    handleTakeoff() {
      if (this.currentMode === 'flying') return

      this.currentMode = 'flying'
      this.simulateDroneAction('起飞中...', 2000, () => {
        this.currentMode = 'hovering'
        this.updateDroneStatus('悬停中')
      })
    },
    handleLanding() {
      if (this.currentMode === 'grounded') return

      this.simulateDroneAction('降落中...', 3000, () => {
        this.currentMode = 'grounded'
        this.clearActiveButtons()
        this.updateDroneStatus('已着陆')
      })
    },
    handleHover() {
      if (this.currentMode === 'grounded') return

      this.currentMode = 'hovering'
      this.updateDroneStatus('悬停中')
    },
    handleReturnHome() {
      if (this.currentMode === 'grounded') return

      this.currentMode = 'returning'
      this.simulateDroneAction('返航中...', 5000, () => {
        this.currentMode = 'grounded'
        this.clearActiveButtons()
        this.updateDroneStatus('返航完成')

        // 地图上无人机返回起始位置
        if (this.droneMarker) {
          this.droneMarker.setLatLng([39.9042, 116.4074])
        }
      })
    },
    handleEmergencyStop() {
      this.currentMode = 'emergency'
      this.updateDroneStatus('紧急停止')

      // 紧急停止后2秒自动解除
      setTimeout(() => {
        this.currentMode = 'hovering'
        this.updateDroneStatus('等待指令')
        this.clearActiveButtons()
      }, 2000)
    },
    handleLock(control) {
      const isLocked = control.active
      if (isLocked) {
        this.updateDroneStatus('解锁成功')
      } else {
        this.updateDroneStatus('已锁定')
      }
    },
    clearActiveButtons() {
      this.droneControls.forEach(ctrl => {
        ctrl.active = false
      })
    },
    simulateDroneAction(message, duration, callback) {
      this.updateDroneStatus(message)
      setTimeout(callback, duration)
    },
    updateDroneStatus(status) {
      console.log('无人机状态:', status)
      this.showStatusToast(status)

      // 更新地图上的无人机弹窗信息
      if (this.droneMarker) {
        this.droneMarker.setPopupContent(`
          <div style="color: #7ed321; font-weight: bold;">
            <i class="fas fa-helicopter"></i> 无人机 A1<br>
            <small>状态: ${status}</small><br>
            <small>高度: 120m</small><br>
            <small>电量: 95%</small>
          </div>
        `)
      }
    },
    showStatusToast(message) {
      this.statusMessage = message
      setTimeout(() => {
        this.statusMessage = ''
      }, 2000)
    }
  }
}
</script>

<style scoped>
/* ================================ 中央地图区域 ================================ */
.map-container {
    flex: 1;
    position: relative;
    background:
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(126,211,33,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)" /></svg>'),
        radial-gradient(circle at 30% 70%, rgba(126, 211, 33, 0.05) 0%, transparent 50%),
        linear-gradient(135deg, #1a2332 0%, #0f1a29 100%);
}

/* 真实地图显示区域 */
#realMap {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

/* 自定义地图样式 */
:deep(.leaflet-container) {
    background: #1a2332 !important;
}

/* 隐藏Leaflet默认的放大缩小按钮，使用我们自己的 */
:deep(.leaflet-control-zoom) {
    display: none !important;
}

/* 自定义地图标记样式 */
:deep(.custom-drone-marker) {
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, rgba(126, 211, 33, 0.8) 30%, transparent 70%);
    border: 3px solid #7ed321;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #7ed321;
    animation: drone-pulse 2s infinite;
    box-shadow: 0 0 20px rgba(126, 211, 33, 0.5);
}

@keyframes drone-pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

/* 任务区域自定义样式 */
:deep(.custom-mission-marker) {
    background: linear-gradient(135deg, rgba(126, 211, 33, 0.2), rgba(126, 211, 33, 0.1));
    border: 2px dashed #7ed321;
    padding: 15px;
    border-radius: 8px;
    color: #7ed321;
    font-size: 12px;
    text-align: center;
    min-width: 120px;
    backdrop-filter: blur(5px);
}

.map-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 60% 40%, rgba(65, 105, 225, 0.08) 0%, transparent 60%),
        linear-gradient(45deg,
            rgba(26, 35, 50, 0.6) 0%,
            rgba(20, 30, 45, 0.3) 50%,
            rgba(26, 35, 50, 0.6) 100%);
}

.map-header {
    position: absolute;
    top: 15px;
    left: 20px;
    right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 10;
    background: rgba(15, 25, 40, 0.9);
    padding: 12px 20px;
    border-radius: 8px;
    border: 1px solid rgba(126, 211, 33, 0.3);
    backdrop-filter: blur(10px);
}

.header-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
}

.header-item i {
    color: #7ed321;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: #7ed321;
    border-radius: 50%;
    animation: status-blink 2s infinite;
}

@keyframes status-blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* 无人机标记增强 */
.drone-marker {
    position: absolute;
    width: 60px;
    height: 60px;
    transform: translate(-50%, -50%);
    z-index: 5;
    transition: all 0.3s ease;
}

.drone-radar {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
    height: 100px;
    border: 2px solid rgba(126, 211, 33, 0.3);
    border-radius: 50%;
    animation: radar-pulse 3s infinite;
}

@keyframes radar-pulse {
    0% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
    }
}

.drone-icon-container {
    position: relative;
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, rgba(126, 211, 33, 0.3) 30%, transparent 70%);
    border: 3px solid #7ed321;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    animation: drone-float 4s ease-in-out infinite;
    box-shadow:
        0 0 20px rgba(126, 211, 33, 0.5),
        inset 0 0 10px rgba(126, 211, 33, 0.2);
}

@keyframes drone-float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

.drone-direction {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    color: #7ed321;
    font-size: 16px;
    filter: drop-shadow(0 0 5px rgba(126, 211, 33, 0.7));
}

.drone-trail {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(126, 211, 33, 0.5), transparent);
    transform: translate(-50%, -50%) rotate(-45deg);
    animation: trail-flow 2s linear infinite;
}

@keyframes trail-flow {
    0% { opacity: 0; transform: translate(-50%, -50%) rotate(-45deg) translateX(-100px); }
    50% { opacity: 1; }
    100% { opacity: 0; transform: translate(-50%, -50%) rotate(-45deg) translateX(100px); }
}

/* 任务区域增强 */
.mission-marker {
    position: absolute;
    transform: translate(-50%, -50%);
    z-index: 4;
}

.mission-box {
    background: linear-gradient(135deg, rgba(126, 211, 33, 0.15), rgba(126, 211, 33, 0.05));
    border: 2px dashed #7ed321;
    padding: 20px;
    border-radius: 12px;
    min-width: 180px;
    text-align: center;
    position: relative;
    backdrop-filter: blur(5px);
}

.mission-box::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(45deg, transparent, rgba(126, 211, 33, 0.1), transparent);
    border-radius: 12px;
    z-index: -1;
    animation: mission-glow 3s ease-in-out infinite;
}

@keyframes mission-glow {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

.mission-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #7ed321;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
}

.mission-info {
    background: rgba(0, 0, 0, 0.8);
    color: #7ed321;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 11px;
    margin-top: 10px;
    border: 1px solid rgba(126, 211, 33, 0.3);
}

/* 无人机操控按钮组 */
.drone-controls {
    position: absolute;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 10;
    padding: 15px;
    background: rgba(15, 25, 40, 0.95);
    border: 1px solid rgba(126, 211, 33, 0.4);
    border-radius: 12px;
    backdrop-filter: blur(15px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.drone-control-btn {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, rgba(126, 211, 33, 0.1), rgba(15, 25, 40, 0.8));
    border: 2px solid rgba(126, 211, 33, 0.5);
    border-radius: 12px;
    color: #7ed321;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 11px;
    font-weight: bold;
    position: relative;
    overflow: hidden;
}

.drone-control-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(126, 211, 33, 0.3), transparent);
    transition: left 0.5s ease;
}

.drone-control-btn:hover::before {
    left: 100%;
}

.drone-control-btn:hover {
    background: linear-gradient(135deg, rgba(126, 211, 33, 0.25), rgba(15, 25, 40, 0.9));
    border-color: #7ed321;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(126, 211, 33, 0.4);
}

.drone-control-btn.active {
    background: linear-gradient(135deg, #7ed321, #9fff40);
    color: #000;
    border-color: #9fff40;
    box-shadow: 0 0 20px rgba(126, 211, 33, 0.7);
}

.drone-control-btn.emergency {
    border-color: #ff6b47;
    color: #ff6b47;
}

.drone-control-btn.emergency:hover {
    background: linear-gradient(135deg, rgba(255, 107, 71, 0.25), rgba(15, 25, 40, 0.9));
    border-color: #ff6b47;
    box-shadow: 0 6px 20px rgba(255, 107, 71, 0.4);
}

.drone-control-btn.emergency.active {
    background: linear-gradient(135deg, #ff6b47, #ff8a73);
    color: #fff;
    border-color: #ff8a73;
    box-shadow: 0 0 20px rgba(255, 107, 71, 0.7);
}

.drone-control-btn i {
    font-size: 28px;
    margin-bottom: 6px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.drone-control-btn .btn-label {
    font-size: 10px;
    text-align: center;
    line-height: 1.2;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.map-control-btn {
    width: 44px;
    height: 44px;
    background: rgba(15, 25, 40, 0.9);
    border: 1px solid rgba(126, 211, 33, 0.4);
    border-radius: 8px;
    color: #7ed321;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 16px;
    backdrop-filter: blur(10px);
}

.map-control-btn:hover {
    background: rgba(126, 211, 33, 0.2);
    border-color: #7ed321;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(126, 211, 33, 0.3);
}

.terrain-map {
    position: absolute;
    bottom: 80px;
    left: 20px;
    width: 240px;
    height: 140px;
    background: rgba(15, 25, 40, 0.95);
    border: 1px solid rgba(126, 211, 33, 0.4);
    border-radius: 8px;
    z-index: 10;
    backdrop-filter: blur(10px);
    overflow: hidden;
}

/* 地图控制按钮调整到地形图下方 */
.map-controls {
    position: absolute;
    bottom: 20px;
    left: 20px;
    width: 240px;
    display: flex;
    justify-content: center;
    gap: 8px;
    z-index: 10;
}

.terrain-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: rgba(126, 211, 33, 0.1);
    border-bottom: 1px solid rgba(126, 211, 33, 0.3);
    color: #7ed321;
    font-size: 12px;
    font-weight: bold;
}

/* 状态提示 */
.status-toast {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(15, 25, 40, 0.95);
    color: #7ed321;
    padding: 12px 24px;
    border-radius: 8px;
    border: 1px solid rgba(126, 211, 33, 0.5);
    font-size: 14px;
    font-weight: bold;
    z-index: 1000;
    animation: toastFadeIn 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

@keyframes toastFadeIn {
    from { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
    to { opacity: 1; transform: translate(-50%, -50%) scale(1); }
}
</style>
