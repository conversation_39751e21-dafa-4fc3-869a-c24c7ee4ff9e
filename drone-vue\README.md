# 专业无人机地面站控制系统 - Vue.js版本

这是一个基于Vue.js框架开发的专业无人机地面站控制系统，从静态HTML页面完整转换而来，保持了所有原有功能和视觉效果。

## 🚁 项目特性

### 核心功能
- **实时地图监控**: 集成Leaflet.js，支持OpenStreetMap和卫星地图切换
- **多视频流监控**: 支持多路视频流同时显示，包括主视频、红外视频、可见光视频
- **无人机控制**: 完整的无人机操控界面，包括起飞、降落、悬停、返航等功能
- **任务监控**: 实时任务进度跟踪和状态显示
- **数据统计**: 实时数据图表和统计信息展示
- **设备管理**: 多设备状态监控和信息展示

### 技术特点
- **Vue.js 3**: 使用最新的Vue.js框架，组件化开发
- **响应式设计**: 完全响应式布局，适配不同屏幕尺寸
- **实时动画**: 丰富的CSS动画效果，包括雷达扫描、无人机轨迹等
- **免费资源**: 使用完全免费的地图API和测试视频源
- **模块化架构**: 清晰的组件结构，易于维护和扩展

## 📁 项目结构

```
drone-vue/
├── package.json          # 项目依赖配置
├── vite.config.js        # Vite构建配置
├── index.html            # 主HTML文件
├── README.md             # 项目说明文档
└── src/
    ├── main.js           # Vue应用入口
    ├── App.vue           # 主应用组件
    ├── style.css         # 全局样式
    └── components/
        ├── LeftPanel.vue     # 左侧任务控制面板
        ├── MapContainer.vue  # 中央地图区域
        └── RightPanel.vue    # 右侧视频监控面板
```

## 🛠️ 安装和运行

### 环境要求
- Node.js 16.0 或更高版本
- npm 或 yarn 包管理器

### 安装步骤

1. **进入项目目录**
   ```bash
   cd drone-vue
   ```

2. **安装依赖**
   ```bash
   npm install
   ```
   或使用yarn:
   ```bash
   yarn install
   ```

3. **启动开发服务器**
   ```bash
   npm run dev
   ```
   或使用yarn:
   ```bash
   yarn dev
   ```

4. **访问应用**
   
   开发服务器启动后，在浏览器中访问：
   ```
   http://localhost:3000
   ```

### 生产构建

构建生产版本：
```bash
npm run build
```

预览生产版本：
```bash
npm run preview
```

## 🎮 功能说明

### 左侧面板 (LeftPanel.vue)
- **任务监控**: 显示当前任务状态和进度
- **无人机列表**: 显示所有无人机的状态信息
  - 信号强度指示器
  - 电池电量显示
  - 系统状态指示灯
- **数据统计**: 实时数据图表和统计信息

### 中央地图区域 (MapContainer.vue)
- **交互式地图**: 基于Leaflet.js的高性能地图
- **图层切换**: OpenStreetMap ↔ 卫星地图
- **无人机标记**: 实时无人机位置和轨迹
- **任务区域**: 任务点标记和信息显示
- **地图控制**: 缩放、定位、图层切换
- **无人机操控**: 起飞、降落、悬停、返航、紧急停止
- **地形图**: 实时地形数据显示

### 右侧面板 (RightPanel.vue)
- **多视频监控**: 
  - 主视频流 (A1)
  - 红外视频流 (A2)  
  - 可见光视频流 (A3)
- **视频控制**: 录制、截图、全屏、设置
- **设备信息**: 实时设备状态和参数
- **姿态指示器**: 无人机姿态角度显示
- **任务点管理**: 任务点状态和进度
- **控制按钮**: 定位、运行状态、预设、详情

## 🔧 技术实现

### 地图集成
- 使用Leaflet.js作为地图引擎
- OpenStreetMap作为默认图层
- ArcGIS卫星图作为备选图层
- 自定义标记和弹窗样式

### 视频流
- HTML5 Video元素
- 多个备用视频源确保可用性
- 自动播放和循环播放
- 错误处理和重试机制

### 动画效果
- CSS3动画和过渡效果
- 雷达扫描动画
- 无人机轨迹动画
- 状态指示灯闪烁
- 数据更新动画

### 数据模拟
- 定时器模拟实时数据更新
- 随机数据变化模拟真实环境
- 无人机移动路径模拟
- 任务进度更新模拟

## 🌐 使用的免费资源

### 地图服务
- **OpenStreetMap**: https://www.openstreetmap.org/
- **ArcGIS卫星图**: https://server.arcgisonline.com/

### 视频源
- **Google测试视频**: https://commondatastorage.googleapis.com/gtv-videos-bucket/
- **示例视频**: https://sample-videos.com/

### 图标库
- **Font Awesome**: https://fontawesome.com/
- **Bootstrap Icons**: https://icons.getbootstrap.com/

## 🚀 部署说明

### 静态部署
构建后的`dist`目录可以直接部署到任何静态文件服务器：
- Nginx
- Apache
- GitHub Pages
- Netlify
- Vercel

### 服务器配置
确保服务器支持单页应用(SPA)路由，配置所有路由都指向`index.html`。

## 📝 开发说明

### 添加新功能
1. 在对应的组件中添加新的数据属性
2. 实现相应的方法
3. 添加必要的样式
4. 测试功能是否正常

### 自定义样式
所有组件都使用scoped样式，可以安全地修改而不影响其他组件。

### 扩展地图功能
可以通过Leaflet.js插件扩展更多地图功能，如热力图、轨迹回放等。

## 🐛 故障排除

### 视频无法播放
- 检查网络连接
- 尝试刷新页面
- 检查浏览器控制台错误信息

### 地图无法加载
- 检查网络连接
- 确认地图服务可用性
- 检查浏览器控制台错误信息

### 样式显示异常
- 清除浏览器缓存
- 检查CSS文件是否正确加载
- 确认浏览器兼容性

## 📄 许可证

MIT License - 详见LICENSE文件

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

---

**注意**: 这是一个演示项目，使用的视频源和地图服务都是免费的公共资源。在生产环境中，请使用适当的商业服务和真实的数据源。
